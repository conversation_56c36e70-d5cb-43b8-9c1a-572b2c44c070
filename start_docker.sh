#!/bin/bash

echo "🐳 Starting AI Training Docker Environment"
echo "=========================================="

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    echo "Visit: https://docs.docker.com/get-docker/"
    exit 1
fi

# Check if Docker Compose is available
if command -v docker-compose &> /dev/null; then
    COMPOSE_CMD="docker-compose"
elif docker compose version &> /dev/null; then
    COMPOSE_CMD="docker compose"
else
    echo "❌ Docker Compose is not available. Please install Docker Compose."
    exit 1
fi

# Check if NVIDIA Docker runtime is available (for GPU support)
if docker info 2>/dev/null | grep -q nvidia; then
    echo "✅ NVIDIA Docker runtime detected - GPU support available"
    GPU_SUPPORT=true
else
    echo "⚠️  NVIDIA Docker runtime not detected - will run on CPU only"
    echo "For GPU support, install nvidia-docker2"
    GPU_SUPPORT=false
fi

# Build and start the container
echo "🔨 Building Docker image..."
$COMPOSE_CMD build

echo "🚀 Starting container..."
if [ "$GPU_SUPPORT" = true ]; then
    $COMPOSE_CMD up -d
else
    # Modify compose file for CPU-only
    sed 's/runtime: nvidia//g' docker-compose.yml > docker-compose-cpu.yml
    sed '/nvidia/d' docker-compose-cpu.yml > docker-compose-cpu-clean.yml
    $COMPOSE_CMD -f docker-compose-cpu-clean.yml up -d
fi

echo "✅ Container started!"
echo ""
echo "📋 Available commands:"
echo "  docker exec -it ai-trainer bash                    # Enter container"
echo "  docker exec -it ai-trainer python3 test_docker_setup.py  # Test setup"
echo "  docker exec -it ai-trainer python3 combined_dataset_training.py --help  # Training help"
echo ""
echo "🔧 To enter the container and start training:"
echo "  docker exec -it ai-trainer bash"
echo ""
echo "🛑 To stop:"
echo "  $COMPOSE_CMD down"
