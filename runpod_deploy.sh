#!/bin/bash

echo "🚀 RunPod AI Training Setup"
echo "=========================="

# Update system
echo "📦 Updating system packages..."
apt-get update -qq

# Install required system packages
echo "🔧 Installing system dependencies..."
apt-get install -y git wget curl vim htop tmux

# Install Python packages
echo "🐍 Installing Python packages..."
pip install --upgrade pip
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121
pip install transformers>=4.36.0 datasets accelerate peft bitsandbytes scipy scikit-learn wandb tensorboard

# Create workspace
echo "📁 Setting up workspace..."
mkdir -p /workspace/ai-training
cd /workspace/ai-training

# Download training scripts (you'll need to upload these)
echo "📥 Ready for training scripts upload..."

echo "✅ Setup complete!"
echo ""
echo "📋 Next steps:"
echo "1. Upload your training scripts to /workspace/ai-training/"
echo "2. Run: python3 combined_dataset_training.py --help"
echo "3. Start training with your preferred parameters"
echo ""
echo "🎯 Recommended training command:"
echo "python3 combined_dataset_training.py \\"
echo "  --output_dir './qwen3-unified-coder' \\"
echo "  --max_samples_per_dataset 3000 \\"
echo "  --num_train_epochs 2 \\"
echo "  --learning_rate 1e-4 \\"
echo "  --per_device_train_batch_size 4 \\"
echo "  --gradient_accumulation_steps 4"
