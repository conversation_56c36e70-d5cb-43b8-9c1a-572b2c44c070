#!/usr/bin/env python3
"""
Training Progress Monitor
Real-time monitoring of training progress with automatic model download
"""

import os
import time
import json
import logging
import subprocess
from datetime import datetime
from typing import Dict, Optional, List
from pathlib import Path

logger = logging.getLogger(__name__)

class TrainingMonitor:
    """Monitor training progress on AWS instance"""
    
    def __init__(self, instance_ip: str, key_file: str):
        self.instance_ip = instance_ip
        self.key_file = key_file
        self.training_started = False
        self.last_log_position = 0
        
    def check_connection(self) -> bool:
        """Check if we can connect to the instance"""
        try:
            cmd = [
                "ssh", "-i", self.key_file, "-o", "StrictHostKeyChecking=no",
                "-o", "ConnectTimeout=10",
                f"ubuntu@{self.instance_ip}",
                "echo 'connected'"
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=15)
            return result.returncode == 0
            
        except Exception as e:
            logger.error(f"Connection check failed: {e}")
            return False
    
    def get_training_status(self) -> Dict:
        """Get current training status"""
        try:
            # Check for status files
            cmd = [
                "ssh", "-i", self.key_file, "-o", "StrictHostKeyChecking=no",
                f"ubuntu@{self.instance_ip}",
                "ls /home/<USER>/training/TRAINING_* 2>/dev/null || echo 'NONE'"
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                output = result.stdout.strip()
                if "TRAINING_COMPLETE" in output:
                    return {"status": "complete", "message": "Training completed successfully"}
                elif "TRAINING_FAILED" in output:
                    return {"status": "failed", "message": "Training failed"}
                elif "NONE" in output:
                    return {"status": "running", "message": "Training in progress"}
            
            return {"status": "unknown", "message": "Unable to determine status"}
            
        except Exception as e:
            logger.error(f"Error getting training status: {e}")
            return {"status": "error", "message": str(e)}
    
    def get_training_logs(self, lines: int = 10) -> List[str]:
        """Get recent training logs"""
        try:
            cmd = [
                "ssh", "-i", self.key_file, "-o", "StrictHostKeyChecking=no",
                f"ubuntu@{self.instance_ip}",
                f"tail -n {lines} /home/<USER>/training/training_output.log 2>/dev/null || echo 'No logs available'"
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                return result.stdout.strip().split('\n')
            
            return ["Unable to retrieve logs"]
            
        except Exception as e:
            logger.error(f"Error getting logs: {e}")
            return [f"Error: {e}"]
    
    def get_system_stats(self) -> Dict:
        """Get system statistics from the instance"""
        try:
            cmd = [
                "ssh", "-i", self.key_file, "-o", "StrictHostKeyChecking=no",
                f"ubuntu@{self.instance_ip}",
                "python3 -c \"import psutil; import json; print(json.dumps({{'cpu': psutil.cpu_percent(), 'memory': psutil.virtual_memory().percent, 'disk': psutil.disk_usage('/').percent}}))\""
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                return json.loads(result.stdout.strip())
            
            return {}
            
        except Exception as e:
            logger.error(f"Error getting system stats: {e}")
            return {}
    
    def get_gpu_stats(self) -> Dict:
        """Get GPU statistics"""
        try:
            cmd = [
                "ssh", "-i", self.key_file, "-o", "StrictHostKeyChecking=no",
                f"ubuntu@{self.instance_ip}",
                "nvidia-smi --query-gpu=utilization.gpu,memory.used,memory.total,temperature.gpu --format=csv,noheader,nounits"
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                values = result.stdout.strip().split(', ')
                if len(values) >= 4:
                    return {
                        'gpu_utilization': int(values[0]),
                        'memory_used': int(values[1]),
                        'memory_total': int(values[2]),
                        'temperature': int(values[3]),
                        'memory_percent': round(int(values[1]) / int(values[2]) * 100, 1)
                    }
            
            return {}
            
        except Exception as e:
            logger.error(f"Error getting GPU stats: {e}")
            return {}
    
    def download_model(self, local_dir: str = "./trained_model") -> bool:
        """Download the trained model"""
        try:
            logger.info("Downloading trained model...")
            
            # Create local directory
            Path(local_dir).mkdir(exist_ok=True)
            
            # Download model files
            cmd = [
                "scp", "-i", self.key_file, "-o", "StrictHostKeyChecking=no",
                "-r", f"ubuntu@{self.instance_ip}:/home/<USER>/training/output/*",
                local_dir
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                logger.info(f"Model downloaded to: {local_dir}")
                return True
            else:
                logger.error(f"Download failed: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"Error downloading model: {e}")
            return False
    
    def monitor_training(self, check_interval: int = 60) -> bool:
        """Monitor training with real-time updates"""
        logger.info("Starting training monitoring...")
        
        start_time = datetime.now()
        
        while True:
            try:
                # Check connection
                if not self.check_connection():
                    logger.error("Lost connection to instance")
                    return False
                
                # Get status
                status = self.get_training_status()
                
                # Get system stats
                system_stats = self.get_system_stats()
                gpu_stats = self.get_gpu_stats()
                
                # Get recent logs
                logs = self.get_training_logs(3)
                
                # Calculate runtime
                runtime = datetime.now() - start_time
                runtime_str = str(runtime).split('.')[0]  # Remove microseconds
                
                # Display status
                print(f"\n🔄 Training Monitor - Runtime: {runtime_str}")
                print("=" * 60)
                
                print(f"📊 Status: {status['status'].upper()} - {status['message']}")
                
                if system_stats:
                    print(f"💻 System: CPU {system_stats.get('cpu', 0):.1f}% | "
                          f"RAM {system_stats.get('memory', 0):.1f}% | "
                          f"Disk {system_stats.get('disk', 0):.1f}%")
                
                if gpu_stats:
                    print(f"🎮 GPU: {gpu_stats.get('gpu_utilization', 0)}% | "
                          f"VRAM {gpu_stats.get('memory_percent', 0):.1f}% "
                          f"({gpu_stats.get('memory_used', 0)}MB/{gpu_stats.get('memory_total', 0)}MB) | "
                          f"Temp {gpu_stats.get('temperature', 0)}°C")
                
                print("📝 Recent Logs:")
                for log_line in logs[-3:]:
                    if log_line.strip():
                        print(f"   {log_line}")
                
                # Check if training is complete
                if status['status'] == 'complete':
                    logger.info("🎉 Training completed successfully!")
                    return True
                elif status['status'] == 'failed':
                    logger.error("❌ Training failed!")
                    return False
                
                # Wait before next check
                time.sleep(check_interval)
                
            except KeyboardInterrupt:
                logger.info("Monitoring interrupted by user")
                return False
            except Exception as e:
                logger.error(f"Monitoring error: {e}")
                time.sleep(check_interval)

class ProgressReporter:
    """Generate progress reports and summaries"""
    
    def __init__(self, monitor: TrainingMonitor):
        self.monitor = monitor
    
    def generate_summary_report(self, start_time: datetime, end_time: datetime) -> str:
        """Generate a summary report of the training session"""
        
        duration = end_time - start_time
        duration_str = str(duration).split('.')[0]
        
        # Get final stats
        final_status = self.monitor.get_training_status()
        system_stats = self.monitor.get_system_stats()
        gpu_stats = self.monitor.get_gpu_stats()
        
        report = f"""
🎯 Training Session Summary
{'=' * 50}

⏱️  Duration: {duration_str}
📅 Start Time: {start_time.strftime('%Y-%m-%d %H:%M:%S')}
📅 End Time: {end_time.strftime('%Y-%m-%d %H:%M:%S')}

📊 Final Status: {final_status['status'].upper()}
💬 Message: {final_status['message']}

🖥️  Final System Stats:
   • CPU Usage: {system_stats.get('cpu', 'N/A')}%
   • Memory Usage: {system_stats.get('memory', 'N/A')}%
   • Disk Usage: {system_stats.get('disk', 'N/A')}%

🎮 Final GPU Stats:
   • GPU Utilization: {gpu_stats.get('gpu_utilization', 'N/A')}%
   • VRAM Usage: {gpu_stats.get('memory_percent', 'N/A')}%
   • Temperature: {gpu_stats.get('temperature', 'N/A')}°C

{'=' * 50}
"""
        return report

def main():
    """Example usage"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Monitor AWS training instance")
    parser.add_argument("--ip", required=True, help="Instance IP address")
    parser.add_argument("--key", required=True, help="SSH key file path")
    parser.add_argument("--interval", type=int, default=60, help="Check interval in seconds")
    
    args = parser.parse_args()
    
    monitor = TrainingMonitor(args.ip, args.key)
    
    start_time = datetime.now()
    success = monitor.monitor_training(args.interval)
    end_time = datetime.now()
    
    # Generate summary
    reporter = ProgressReporter(monitor)
    summary = reporter.generate_summary_report(start_time, end_time)
    print(summary)
    
    # Download model if successful
    if success:
        monitor.download_model()

if __name__ == "__main__":
    main()
