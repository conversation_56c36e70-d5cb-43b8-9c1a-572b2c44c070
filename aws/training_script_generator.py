#!/usr/bin/env python3
"""
Training Script Generator for Qwen3-8B
Generates complete training scripts with dataset combination and LoRA configuration
"""

import os
import json
from typing import Dict, List, Optional
from dataclasses import dataclass, asdict

@dataclass
class TrainingConfig:
    """Configuration for training"""
    model_name: str = "Qwen/Qwen3-8B"
    output_dir: str = "/home/<USER>/training/output"
    
    # Dataset configuration
    datasets: List[Dict] = None
    max_samples_per_dataset: int = 2000
    
    # Training parameters
    num_epochs: int = 2
    batch_size: int = 4
    gradient_accumulation_steps: int = 4
    learning_rate: float = 2e-4
    warmup_steps: int = 100
    
    # LoRA configuration
    lora_rank: int = 16
    lora_alpha: int = 32
    lora_dropout: float = 0.1
    
    # Quantization
    use_4bit: bool = True
    bnb_4bit_compute_dtype: str = "float16"
    
    # System
    max_seq_length: int = 2048
    save_steps: int = 500
    logging_steps: int = 10
    
    def __post_init__(self):
        if self.datasets is None:
            self.datasets = [
                {
                    "name": "iamtarun/python_code_instructions_18k_alpaca",
                    "split": "train",
                    "max_samples": 2000,
                    "description": "Python coding instructions"
                },
                {
                    "name": "codeparrot/github-code-clean",
                    "split": "train",
                    "max_samples": 2000,
                    "description": "Clean GitHub code samples"
                },
                {
                    "name": "bigcode/the-stack-dedup",
                    "split": "train",
                    "max_samples": 2000,
                    "description": "Deduplicated code stack",
                    "subset": "python"
                }
            ]

class TrainingScriptGenerator:
    """Generates complete training scripts for AWS deployment"""
    
    def __init__(self, config: TrainingConfig):
        self.config = config
    
    def generate_requirements_txt(self) -> str:
        """Generate requirements.txt for the training environment"""
        return """# Core ML libraries
torch>=2.1.0
transformers>=4.45.0
datasets>=2.14.0
tokenizers>=0.15.0

# Training and optimization
trl>=0.11.0
peft>=0.12.0
accelerate>=0.25.0
bitsandbytes>=0.41.0

# Utilities
numpy>=1.24.0
scipy>=1.10.0

# Data processing
pandas>=2.0.0

# System utilities
psutil>=5.9.0

# HuggingFace Hub
huggingface-hub>=0.19.0

# AWS SDK
boto3>=1.35.0

# Progress tracking
tqdm>=4.66.0
"""
    
    def generate_training_script(self) -> str:
        """Generate the main training script"""
        return f'''#!/usr/bin/env python3
"""
Qwen3-8B Training Script for AWS
Auto-generated training script with dataset combination and LoRA
"""

import os
import sys
import json
import torch
import logging
from datetime import datetime
from typing import Dict, List, Optional

# Core ML libraries
from transformers import (
    AutoTokenizer,
    AutoModelForCausalLM,
    BitsAndBytesConfig,
    TrainingArguments,
    Trainer,
    DataCollatorForLanguageModeling
)
from datasets import Dataset, concatenate_datasets, load_dataset
from peft import LoraConfig, get_peft_model, TaskType
import boto3

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/home/<USER>/training/training.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class Qwen3Trainer:
    """Qwen3-8B trainer with LoRA and dataset combination"""
    
    def __init__(self):
        self.config = {json.dumps(asdict(self.config), indent=8)}
        self.tokenizer = None
        self.model = None
        self.datasets = []
        
    def setup_model_and_tokenizer(self):
        """Setup model and tokenizer with quantization"""
        logger.info("Setting up model and tokenizer...")
        
        # Quantization config
        bnb_config = BitsAndBytesConfig(
            load_in_4bit=self.config["use_4bit"],
            bnb_4bit_compute_dtype=torch.float16,
            bnb_4bit_quant_type="nf4",
            bnb_4bit_use_double_quant=True,
        )
        
        # Load tokenizer
        self.tokenizer = AutoTokenizer.from_pretrained(
            self.config["model_name"],
            trust_remote_code=True
        )
        
        # Add pad token if not present
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
        
        # Load model
        self.model = AutoModelForCausalLM.from_pretrained(
            self.config["model_name"],
            quantization_config=bnb_config,
            device_map="auto",
            trust_remote_code=True,
            torch_dtype=torch.float16
        )
        
        # Setup LoRA
        lora_config = LoraConfig(
            task_type=TaskType.CAUSAL_LM,
            r=self.config["lora_rank"],
            lora_alpha=self.config["lora_alpha"],
            lora_dropout=self.config["lora_dropout"],
            target_modules=["q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"]
        )
        
        self.model = get_peft_model(self.model, lora_config)
        self.model.print_trainable_parameters()
        
        logger.info("Model and tokenizer setup complete")
    
    def load_and_combine_datasets(self) -> Dataset:
        """Load and combine multiple datasets"""
        logger.info("Loading and combining datasets...")
        
        combined_data = []
        
        for dataset_config in self.config["datasets"]:
            try:
                logger.info(f"Loading dataset: {{dataset_config['name']}}")
                
                # Load dataset
                if "subset" in dataset_config:
                    dataset = load_dataset(
                        dataset_config["name"], 
                        dataset_config["subset"],
                        split=dataset_config["split"]
                    )
                else:
                    dataset = load_dataset(
                        dataset_config["name"], 
                        split=dataset_config["split"]
                    )
                
                # Sample if needed
                max_samples = dataset_config.get("max_samples", len(dataset))
                if len(dataset) > max_samples:
                    dataset = dataset.shuffle(seed=42).select(range(max_samples))
                
                # Convert to standard format
                formatted_data = self._format_dataset(dataset, dataset_config)
                combined_data.extend(formatted_data)
                
                logger.info(f"Added {{len(formatted_data)}} samples from {{dataset_config['name']}}")
                
            except Exception as e:
                logger.error(f"Error loading dataset {{dataset_config['name']}}: {{e}}")
                continue
        
        # Create combined dataset
        if not combined_data:
            raise ValueError("No datasets were successfully loaded")
        
        combined_dataset = Dataset.from_list(combined_data)
        logger.info(f"Combined dataset size: {{len(combined_dataset)}} samples")
        
        return combined_dataset
    
    def _format_dataset(self, dataset, config: Dict) -> List[Dict]:
        """Format dataset to standard instruction format"""
        formatted_data = []
        
        for item in dataset:
            try:
                # Try to extract instruction and response
                if "instruction" in item and "output" in item:
                    # Alpaca format
                    instruction = item["instruction"]
                    response = item["output"]
                    input_text = item.get("input", "")
                    
                elif "prompt" in item and "completion" in item:
                    # Prompt-completion format
                    instruction = item["prompt"]
                    response = item["completion"]
                    input_text = ""
                    
                elif "text" in item:
                    # Raw text format - split into instruction/response
                    text = item["text"]
                    if "def " in text or "class " in text:
                        instruction = "Write Python code for the following:"
                        response = text
                        input_text = ""
                    else:
                        continue
                        
                else:
                    continue
                
                # Format as instruction-following
                if input_text:
                    full_instruction = f"{{instruction}}\\n\\nInput: {{input_text}}"
                else:
                    full_instruction = instruction
                
                formatted_item = {{
                    "instruction": full_instruction,
                    "response": response,
                    "source": config["name"]
                }}
                
                formatted_data.append(formatted_item)
                
            except Exception as e:
                continue
        
        return formatted_data
    
    def tokenize_dataset(self, dataset: Dataset) -> Dataset:
        """Tokenize the dataset"""
        logger.info("Tokenizing dataset...")
        
        def tokenize_function(examples):
            # Format as instruction-following
            texts = []
            for instruction, response in zip(examples["instruction"], examples["response"]):
                text = f"<|im_start|>user\\n{{instruction}}<|im_end|>\\n<|im_start|>assistant\\n{{response}}<|im_end|>"
                texts.append(text)
            
            # Tokenize
            tokenized = self.tokenizer(
                texts,
                truncation=True,
                padding=False,
                max_length=self.config["max_seq_length"],
                return_tensors=None
            )
            
            # Set labels for language modeling
            tokenized["labels"] = tokenized["input_ids"].copy()
            
            return tokenized
        
        tokenized_dataset = dataset.map(
            tokenize_function,
            batched=True,
            remove_columns=dataset.column_names
        )
        
        logger.info("Dataset tokenization complete")
        return tokenized_dataset
    
    def train(self):
        """Main training function"""
        logger.info("Starting Qwen3-8B training...")
        
        # Setup model
        self.setup_model_and_tokenizer()
        
        # Load and prepare data
        dataset = self.load_and_combine_datasets()
        tokenized_dataset = self.tokenize_dataset(dataset)
        
        # Split dataset
        train_size = int(0.95 * len(tokenized_dataset))
        eval_size = len(tokenized_dataset) - train_size
        
        train_dataset = tokenized_dataset.select(range(train_size))
        eval_dataset = tokenized_dataset.select(range(train_size, train_size + eval_size))
        
        logger.info(f"Train samples: {{len(train_dataset)}}, Eval samples: {{len(eval_dataset)}}")
        
        # Training arguments
        training_args = TrainingArguments(
            output_dir=self.config["output_dir"],
            num_train_epochs=self.config["num_epochs"],
            per_device_train_batch_size=self.config["batch_size"],
            per_device_eval_batch_size=self.config["batch_size"],
            gradient_accumulation_steps=self.config["gradient_accumulation_steps"],
            learning_rate=self.config["learning_rate"],
            warmup_steps=self.config["warmup_steps"],
            logging_steps=self.config["logging_steps"],
            save_steps=self.config["save_steps"],
            eval_steps=self.config["save_steps"],
            evaluation_strategy="steps",
            save_strategy="steps",
            load_best_model_at_end=True,
            metric_for_best_model="eval_loss",
            greater_is_better=False,
            fp16=True,
            dataloader_pin_memory=False,
            remove_unused_columns=False,
            report_to=None,  # Disable wandb
        )
        
        # Data collator
        data_collator = DataCollatorForLanguageModeling(
            tokenizer=self.tokenizer,
            mlm=False
        )
        
        # Trainer
        trainer = Trainer(
            model=self.model,
            args=training_args,
            train_dataset=train_dataset,
            eval_dataset=eval_dataset,
            data_collator=data_collator,
        )
        
        # Train
        logger.info("Starting training...")
        trainer.train()
        
        # Save final model
        logger.info("Saving final model...")
        trainer.save_model()
        self.tokenizer.save_pretrained(self.config["output_dir"])
        
        # Save training info
        training_info = {{
            "model_name": self.config["model_name"],
            "datasets": self.config["datasets"],
            "training_config": self.config,
            "final_eval_loss": trainer.state.log_history[-1].get("eval_loss", "N/A"),
            "total_steps": trainer.state.global_step,
            "training_time": str(datetime.now())
        }}
        
        with open(os.path.join(self.config["output_dir"], "training_info.json"), "w") as f:
            json.dump(training_info, f, indent=2)
        
        logger.info("Training complete!")
        return self.config["output_dir"]

def upload_to_s3(local_path: str, bucket: str, key: str):
    """Upload trained model to S3"""
    try:
        s3_client = boto3.client('s3')
        
        # Create tar.gz of the model directory
        import tarfile
        tar_path = "/tmp/trained_model.tar.gz"
        
        with tarfile.open(tar_path, "w:gz") as tar:
            tar.add(local_path, arcname=os.path.basename(local_path))
        
        # Upload to S3
        s3_client.upload_file(tar_path, bucket, key)
        logger.info(f"Model uploaded to s3://{{bucket}}/{{key}}")
        
        # Generate presigned URL for download
        url = s3_client.generate_presigned_url(
            'get_object',
            Params={{'Bucket': bucket, 'Key': key}},
            ExpiresIn=3600  # 1 hour
        )
        
        logger.info(f"Download URL: {{url}}")
        return url
        
    except Exception as e:
        logger.error(f"Error uploading to S3: {{e}}")
        return None

if __name__ == "__main__":
    try:
        # Create output directory
        os.makedirs("/home/<USER>/training/output", exist_ok=True)
        
        # Initialize trainer
        trainer = Qwen3Trainer()
        
        # Train model
        output_dir = trainer.train()
        
        # Upload to S3 (optional)
        bucket_name = os.environ.get("S3_BUCKET", "ai-training-models")
        model_key = f"qwen3-trained-{{datetime.now().strftime('%Y%m%d-%H%M%S')}}.tar.gz"
        
        upload_url = upload_to_s3(output_dir, bucket_name, model_key)
        
        # Signal completion
        with open("/home/<USER>/training/TRAINING_COMPLETE", "w") as f:
            f.write(f"Training completed at {{datetime.now()}}\\n")
            f.write(f"Model saved to: {{output_dir}}\\n")
            if upload_url:
                f.write(f"Download URL: {{upload_url}}\\n")
        
        logger.info("All tasks completed successfully!")
        
    except Exception as e:
        logger.error(f"Training failed: {{e}}")
        with open("/home/<USER>/training/TRAINING_FAILED", "w") as f:
            f.write(f"Training failed at {{datetime.now()}}\\n")
            f.write(f"Error: {{str(e)}}\\n")
        sys.exit(1)
'''
    
    def generate_setup_script(self) -> str:
        """Generate setup script for the instance"""
        return '''#!/bin/bash
set -e

echo "Setting up training environment..."

# Update system
sudo apt-get update -y

# Install Python dependencies
cd /home/<USER>/training
pip3 install -r requirements.txt

# Set up CUDA environment
export CUDA_HOME=/usr/local/cuda
export PATH=$CUDA_HOME/bin:$PATH
export LD_LIBRARY_PATH=$CUDA_HOME/lib64:$LD_LIBRARY_PATH

# Verify GPU
nvidia-smi

echo "Setup complete!"
'''
    
    def generate_all_files(self, output_dir: str = "/tmp/training_scripts"):
        """Generate all training files"""
        os.makedirs(output_dir, exist_ok=True)
        
        files = {
            "requirements.txt": self.generate_requirements_txt(),
            "train_qwen3.py": self.generate_training_script(),
            "setup.sh": self.generate_setup_script()
        }
        
        for filename, content in files.items():
            filepath = os.path.join(output_dir, filename)
            with open(filepath, 'w') as f:
                f.write(content)
            
            # Make shell scripts executable
            if filename.endswith('.sh'):
                os.chmod(filepath, 0o755)
        
        return output_dir

if __name__ == "__main__":
    config = TrainingConfig()
    generator = TrainingScriptGenerator(config)
    
    output_dir = generator.generate_all_files()
    print(f"Training scripts generated in: {output_dir}")
