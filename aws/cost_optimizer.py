#!/usr/bin/env python3
"""
AWS Cost Optimization Engine
Handles spot instance management, auto-termination, and cost tracking
"""

import boto3
import time
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class CostReport:
    """Cost tracking report"""
    instance_id: str
    instance_type: str
    launch_time: datetime
    termination_time: Optional[datetime]
    total_runtime_hours: float
    estimated_cost: float
    actual_cost: Optional[float]
    spot_savings: float
    cost_breakdown: Dict[str, float]

class CostOptimizer:
    """AWS cost optimization and tracking"""
    
    def __init__(self, region: str = "us-east-1"):
        self.region = region
        self.ec2_client = boto3.client('ec2', region_name=region)
        self.pricing_client = boto3.client('pricing', region_name='us-east-1')  # Pricing API only in us-east-1
        
        # Pricing cache
        self.pricing_cache = {}
        
    def get_spot_price_history(self, instance_type: str, days: int = 7) -> List[Dict]:
        """Get spot price history for instance type"""
        try:
            end_time = datetime.utcnow()
            start_time = end_time - timedelta(days=days)
            
            response = self.ec2_client.describe_spot_price_history(
                InstanceTypes=[instance_type],
                ProductDescriptions=['Linux/UNIX'],
                StartTime=start_time,
                EndTime=end_time
            )
            
            return response['SpotPriceHistory']
            
        except Exception as e:
            logger.error(f"Error getting spot price history: {e}")
            return []
    
    def get_current_spot_price(self, instance_type: str) -> float:
        """Get current spot price for instance type"""
        try:
            history = self.get_spot_price_history(instance_type, days=1)
            if history:
                return float(history[0]['SpotPrice'])
            return 0.0
        except Exception as e:
            logger.error(f"Error getting current spot price: {e}")
            return 0.0
    
    def get_on_demand_price(self, instance_type: str) -> float:
        """Get on-demand price for instance type"""
        if instance_type in self.pricing_cache:
            return self.pricing_cache[instance_type]
        
        try:
            # Hardcoded prices for common instance types (2025 pricing)
            pricing_map = {
                'g4dn.xlarge': 0.526,
                'g5.xlarge': 1.006,
                'g5.2xlarge': 1.212,
                'p3.2xlarge': 3.060,
                'p3.8xlarge': 12.240,
                'p4d.24xlarge': 32.772
            }
            
            if instance_type in pricing_map:
                price = pricing_map[instance_type]
                self.pricing_cache[instance_type] = price
                return price
            
            # Fallback to pricing API (more complex)
            response = self.pricing_client.get_products(
                ServiceCode='AmazonEC2',
                Filters=[
                    {'Type': 'TERM_MATCH', 'Field': 'instanceType', 'Value': instance_type},
                    {'Type': 'TERM_MATCH', 'Field': 'location', 'Value': 'US East (N. Virginia)'},
                    {'Type': 'TERM_MATCH', 'Field': 'tenancy', 'Value': 'Shared'},
                    {'Type': 'TERM_MATCH', 'Field': 'operating-system', 'Value': 'Linux'},
                ]
            )
            
            if response['PriceList']:
                price_data = json.loads(response['PriceList'][0])
                terms = price_data['terms']['OnDemand']
                for term in terms.values():
                    for price_dimension in term['priceDimensions'].values():
                        price = float(price_dimension['pricePerUnit']['USD'])
                        self.pricing_cache[instance_type] = price
                        return price
            
            return 0.0
            
        except Exception as e:
            logger.error(f"Error getting on-demand price: {e}")
            return 0.0
    
    def calculate_spot_savings(self, instance_type: str, runtime_hours: float) -> Tuple[float, float, float]:
        """Calculate potential savings with spot instances"""
        on_demand_price = self.get_on_demand_price(instance_type)
        current_spot_price = self.get_current_spot_price(instance_type)
        
        on_demand_cost = on_demand_price * runtime_hours
        spot_cost = current_spot_price * runtime_hours
        savings = on_demand_cost - spot_cost
        savings_percentage = (savings / on_demand_cost * 100) if on_demand_cost > 0 else 0
        
        return on_demand_cost, spot_cost, savings_percentage
    
    def recommend_instance_type(self, budget: float, runtime_hours: float = 2.0) -> List[Dict]:
        """Recommend instance types based on budget"""
        instance_types = ['g4dn.xlarge', 'g5.xlarge', 'g5.2xlarge', 'p3.2xlarge']
        recommendations = []
        
        for instance_type in instance_types:
            on_demand_price = self.get_on_demand_price(instance_type)
            spot_price = self.get_current_spot_price(instance_type)
            
            on_demand_cost = on_demand_price * runtime_hours
            spot_cost = spot_price * runtime_hours
            
            if spot_cost <= budget:
                recommendations.append({
                    'instance_type': instance_type,
                    'on_demand_cost': on_demand_cost,
                    'spot_cost': spot_cost,
                    'savings': on_demand_cost - spot_cost,
                    'savings_percentage': ((on_demand_cost - spot_cost) / on_demand_cost * 100) if on_demand_cost > 0 else 0,
                    'fits_budget': True
                })
            else:
                recommendations.append({
                    'instance_type': instance_type,
                    'on_demand_cost': on_demand_cost,
                    'spot_cost': spot_cost,
                    'savings': on_demand_cost - spot_cost,
                    'savings_percentage': ((on_demand_cost - spot_cost) / on_demand_cost * 100) if on_demand_cost > 0 else 0,
                    'fits_budget': False
                })
        
        # Sort by cost efficiency
        recommendations.sort(key=lambda x: x['spot_cost'])
        return recommendations
    
    def setup_auto_termination(self, instance_id: str, max_runtime_hours: int = 4):
        """Setup auto-termination for instance"""
        try:
            # Create CloudWatch alarm for auto-termination
            cloudwatch = boto3.client('cloudwatch', region_name=self.region)
            
            alarm_name = f"auto-terminate-{instance_id}"
            
            cloudwatch.put_metric_alarm(
                AlarmName=alarm_name,
                ComparisonOperator='LessThanThreshold',
                EvaluationPeriods=2,
                MetricName='CPUUtilization',
                Namespace='AWS/EC2',
                Period=300,  # 5 minutes
                Statistic='Average',
                Threshold=5.0,  # Less than 5% CPU
                ActionsEnabled=True,
                AlarmActions=[
                    f'arn:aws:automate:{self.region}:ec2:terminate'
                ],
                AlarmDescription='Auto-terminate instance on low CPU usage',
                Dimensions=[
                    {
                        'Name': 'InstanceId',
                        'Value': instance_id
                    },
                ],
                Unit='Percent'
            )
            
            logger.info(f"Auto-termination alarm created: {alarm_name}")
            
            # Also set up a maximum runtime alarm
            max_alarm_name = f"max-runtime-{instance_id}"
            
            # This would require a custom metric, for now just log
            logger.info(f"Instance will auto-terminate after {max_runtime_hours} hours")
            
        except Exception as e:
            logger.error(f"Error setting up auto-termination: {e}")
    
    def track_instance_costs(self, instance_id: str) -> CostReport:
        """Track costs for a specific instance"""
        try:
            # Get instance details
            response = self.ec2_client.describe_instances(InstanceIds=[instance_id])
            instance = response['Reservations'][0]['Instances'][0]
            
            instance_type = instance['InstanceType']
            launch_time = instance['LaunchTime']
            state = instance['State']['Name']
            
            # Calculate runtime
            if state == 'terminated':
                # Try to get termination time from state transition
                termination_time = datetime.utcnow()  # Approximation
            else:
                termination_time = None
            
            current_time = termination_time or datetime.utcnow()
            runtime = current_time - launch_time.replace(tzinfo=None)
            runtime_hours = runtime.total_seconds() / 3600
            
            # Get pricing
            on_demand_price = self.get_on_demand_price(instance_type)
            spot_price = self.get_current_spot_price(instance_type)
            
            # Check if it's a spot instance
            is_spot = 'SpotInstanceRequestId' in instance
            
            if is_spot:
                estimated_cost = spot_price * runtime_hours
                spot_savings = (on_demand_price - spot_price) * runtime_hours
            else:
                estimated_cost = on_demand_price * runtime_hours
                spot_savings = 0.0
            
            # Cost breakdown
            cost_breakdown = {
                'compute': estimated_cost,
                'storage': 0.1 * runtime_hours / 24,  # Approximate EBS cost
                'data_transfer': 0.05,  # Approximate
            }
            
            total_estimated_cost = sum(cost_breakdown.values())
            
            report = CostReport(
                instance_id=instance_id,
                instance_type=instance_type,
                launch_time=launch_time.replace(tzinfo=None),
                termination_time=termination_time,
                total_runtime_hours=runtime_hours,
                estimated_cost=total_estimated_cost,
                actual_cost=None,  # Would need billing API
                spot_savings=spot_savings,
                cost_breakdown=cost_breakdown
            )
            
            return report
            
        except Exception as e:
            logger.error(f"Error tracking instance costs: {e}")
            return None
    
    def optimize_spot_bid(self, instance_type: str, target_availability: float = 0.95) -> float:
        """Optimize spot bid price for target availability"""
        try:
            # Get price history
            history = self.get_spot_price_history(instance_type, days=30)
            
            if not history:
                return self.get_on_demand_price(instance_type) * 0.5
            
            # Calculate price percentiles
            prices = [float(h['SpotPrice']) for h in history]
            prices.sort()
            
            # Find price that gives target availability
            target_index = int(len(prices) * target_availability)
            recommended_bid = prices[min(target_index, len(prices) - 1)]
            
            # Cap at 70% of on-demand price
            on_demand_price = self.get_on_demand_price(instance_type)
            max_bid = on_demand_price * 0.7
            
            return min(recommended_bid, max_bid)
            
        except Exception as e:
            logger.error(f"Error optimizing spot bid: {e}")
            return self.get_on_demand_price(instance_type) * 0.5
    
    def generate_cost_report(self, instance_id: str) -> str:
        """Generate a formatted cost report"""
        report = self.track_instance_costs(instance_id)
        
        if not report:
            return "Unable to generate cost report"
        
        report_text = f"""
🏷️  AWS Training Cost Report
{'=' * 40}

Instance Details:
  • Instance ID: {report.instance_id}
  • Instance Type: {report.instance_type}
  • Launch Time: {report.launch_time.strftime('%Y-%m-%d %H:%M:%S')}
  • Runtime: {report.total_runtime_hours:.2f} hours

Cost Breakdown:
  • Compute: ${report.cost_breakdown['compute']:.2f}
  • Storage: ${report.cost_breakdown['storage']:.2f}
  • Data Transfer: ${report.cost_breakdown['data_transfer']:.2f}
  • Total Estimated: ${report.estimated_cost:.2f}

Savings:
  • Spot Instance Savings: ${report.spot_savings:.2f}
  • Savings Percentage: {(report.spot_savings / (report.estimated_cost + report.spot_savings) * 100):.1f}%

{'=' * 40}
"""
        return report_text

def main():
    """Example usage"""
    optimizer = CostOptimizer()
    
    # Get recommendations for $1 budget
    recommendations = optimizer.recommend_instance_type(budget=1.0)
    
    print("💰 Instance Recommendations for $1.00 budget:")
    print("=" * 50)
    
    for rec in recommendations:
        status = "✅ Fits Budget" if rec['fits_budget'] else "❌ Over Budget"
        print(f"""
Instance Type: {rec['instance_type']}
On-Demand Cost: ${rec['on_demand_cost']:.2f}
Spot Cost: ${rec['spot_cost']:.2f}
Savings: ${rec['savings']:.2f} ({rec['savings_percentage']:.1f}%)
Status: {status}
""")

if __name__ == "__main__":
    main()
