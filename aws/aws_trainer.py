#!/usr/bin/env python3
"""
AWS AI Trainer - Complete Automation Pipeline
Handles the entire training pipeline from instance launch to model download
"""

import os
import sys
import time
import json
import logging
import subprocess
from datetime import datetime
from typing import Dict, Optional
from pathlib import Path

# Import our modules
from instance_manager import AWSInstanceManager, InstanceConfig
from training_script_generator import TrainingScriptGenerator, TrainingConfig

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('aws_training.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class AWSTrainer:
    """Complete AWS training automation"""
    
    def __init__(self, 
                 instance_type: str = "g5.xlarge",
                 use_spot: bool = True,
                 spot_price: str = "0.50"):
        
        # Configuration
        self.instance_config = InstanceConfig(
            instance_type=instance_type,
            use_spot=use_spot,
            spot_price=spot_price
        )
        
        self.training_config = TrainingConfig()
        
        # Managers
        self.instance_manager = AWSInstanceManager(self.instance_config)
        self.script_generator = TrainingScriptGenerator(self.training_config)
        
        # State
        self.instance_id = None
        self.public_ip = None
        self.training_started = False
        
    def check_aws_credentials(self) -> bool:
        """Check if AWS credentials are configured"""
        try:
            import boto3
            sts = boto3.client('sts')
            sts.get_caller_identity()
            logger.info("AWS credentials verified")
            return True
        except Exception as e:
            logger.error(f"AWS credentials not configured: {e}")
            logger.error("Please run: aws configure")
            return False
    
    def generate_training_files(self) -> str:
        """Generate all training files locally"""
        logger.info("Generating training scripts...")
        
        # Create local directory
        script_dir = Path("./generated_scripts")
        script_dir.mkdir(exist_ok=True)
        
        # Generate files
        output_dir = self.script_generator.generate_all_files(str(script_dir))
        
        logger.info(f"Training scripts generated in: {output_dir}")
        return output_dir
    
    def launch_instance(self) -> bool:
        """Launch AWS instance"""
        logger.info("Launching AWS instance...")
        
        try:
            self.instance_id = self.instance_manager.launch_instance()
            
            # Wait for SSH to be ready
            if not self.instance_manager.wait_for_ssh_ready():
                logger.error("SSH not ready, aborting")
                return False
            
            # Get instance info
            info = self.instance_manager.get_instance_info()
            self.public_ip = info['public_ip']
            
            logger.info(f"Instance ready: {self.instance_id} at {self.public_ip}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to launch instance: {e}")
            return False
    
    def upload_training_files(self, script_dir: str) -> bool:
        """Upload training files to instance"""
        logger.info("Uploading training files...")
        
        try:
            key_file = f"{self.instance_config.key_name}.pem"
            
            # Create remote directory
            ssh_cmd = [
                "ssh", "-i", key_file, "-o", "StrictHostKeyChecking=no",
                f"ubuntu@{self.public_ip}",
                "mkdir -p /home/<USER>/training"
            ]
            
            result = subprocess.run(ssh_cmd, capture_output=True, text=True)
            if result.returncode != 0:
                logger.error(f"Failed to create remote directory: {result.stderr}")
                return False
            
            # Upload files
            scp_cmd = [
                "scp", "-i", key_file, "-o", "StrictHostKeyChecking=no",
                "-r", f"{script_dir}/*",
                f"ubuntu@{self.public_ip}:/home/<USER>/training/"
            ]
            
            result = subprocess.run(scp_cmd, capture_output=True, text=True)
            if result.returncode != 0:
                logger.error(f"Failed to upload files: {result.stderr}")
                return False
            
            logger.info("Training files uploaded successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error uploading files: {e}")
            return False
    
    def run_setup(self) -> bool:
        """Run setup script on instance"""
        logger.info("Running setup on instance...")
        
        try:
            key_file = f"{self.instance_config.key_name}.pem"
            
            ssh_cmd = [
                "ssh", "-i", key_file, "-o", "StrictHostKeyChecking=no",
                f"ubuntu@{self.public_ip}",
                "cd /home/<USER>/training && chmod +x setup.sh && ./setup.sh"
            ]
            
            result = subprocess.run(ssh_cmd, capture_output=True, text=True, timeout=600)
            
            if result.returncode != 0:
                logger.error(f"Setup failed: {result.stderr}")
                return False
            
            logger.info("Setup completed successfully")
            return True
            
        except subprocess.TimeoutExpired:
            logger.error("Setup timed out")
            return False
        except Exception as e:
            logger.error(f"Error running setup: {e}")
            return False
    
    def start_training(self) -> bool:
        """Start training on instance"""
        logger.info("Starting training...")
        
        try:
            key_file = f"{self.instance_config.key_name}.pem"
            
            # Start training in background with nohup
            ssh_cmd = [
                "ssh", "-i", key_file, "-o", "StrictHostKeyChecking=no",
                f"ubuntu@{self.public_ip}",
                "cd /home/<USER>/training && nohup python3 train_qwen3.py > training_output.log 2>&1 &"
            ]
            
            result = subprocess.run(ssh_cmd, capture_output=True, text=True)
            
            if result.returncode != 0:
                logger.error(f"Failed to start training: {result.stderr}")
                return False
            
            self.training_started = True
            logger.info("Training started successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error starting training: {e}")
            return False
    
    def monitor_training(self) -> bool:
        """Monitor training progress"""
        logger.info("Monitoring training progress...")
        
        key_file = f"{self.instance_config.key_name}.pem"
        
        while True:
            try:
                # Check for completion files
                ssh_cmd = [
                    "ssh", "-i", key_file, "-o", "StrictHostKeyChecking=no",
                    f"ubuntu@{self.public_ip}",
                    "ls /home/<USER>/training/TRAINING_*"
                ]
                
                result = subprocess.run(ssh_cmd, capture_output=True, text=True)
                
                if "TRAINING_COMPLETE" in result.stdout:
                    logger.info("Training completed successfully!")
                    return True
                elif "TRAINING_FAILED" in result.stdout:
                    logger.error("Training failed!")
                    return False
                
                # Get training logs
                log_cmd = [
                    "ssh", "-i", key_file, "-o", "StrictHostKeyChecking=no",
                    f"ubuntu@{self.public_ip}",
                    "tail -n 5 /home/<USER>/training/training_output.log"
                ]
                
                log_result = subprocess.run(log_cmd, capture_output=True, text=True)
                if log_result.returncode == 0 and log_result.stdout.strip():
                    logger.info(f"Training progress: {log_result.stdout.strip()}")
                
                # Wait before next check
                time.sleep(60)  # Check every minute
                
            except KeyboardInterrupt:
                logger.info("Monitoring interrupted by user")
                return False
            except Exception as e:
                logger.error(f"Error monitoring training: {e}")
                time.sleep(60)
    
    def download_model(self, local_dir: str = "./trained_model") -> bool:
        """Download trained model from instance"""
        logger.info("Downloading trained model...")
        
        try:
            key_file = f"{self.instance_config.key_name}.pem"
            
            # Create local directory
            Path(local_dir).mkdir(exist_ok=True)
            
            # Download model files
            scp_cmd = [
                "scp", "-i", key_file, "-o", "StrictHostKeyChecking=no",
                "-r", f"ubuntu@{self.public_ip}:/home/<USER>/training/output/*",
                local_dir
            ]
            
            result = subprocess.run(scp_cmd, capture_output=True, text=True)
            
            if result.returncode != 0:
                logger.error(f"Failed to download model: {result.stderr}")
                return False
            
            logger.info(f"Model downloaded to: {local_dir}")
            return True
            
        except Exception as e:
            logger.error(f"Error downloading model: {e}")
            return False
    
    def cleanup(self):
        """Clean up resources"""
        logger.info("Cleaning up resources...")
        
        if self.instance_id:
            self.instance_manager.terminate_instance()
            logger.info("Instance terminated")
    
    def run_complete_pipeline(self) -> bool:
        """Run the complete training pipeline"""
        logger.info("Starting complete AWS training pipeline...")
        
        try:
            # Check prerequisites
            if not self.check_aws_credentials():
                return False
            
            # Generate training files
            script_dir = self.generate_training_files()
            
            # Launch instance
            if not self.launch_instance():
                return False
            
            # Upload files
            if not self.upload_training_files(script_dir):
                return False
            
            # Run setup
            if not self.run_setup():
                return False
            
            # Start training
            if not self.start_training():
                return False
            
            # Monitor training
            if not self.monitor_training():
                return False
            
            # Download model
            if not self.download_model():
                return False
            
            logger.info("🎉 Training pipeline completed successfully!")
            return True
            
        except Exception as e:
            logger.error(f"Pipeline failed: {e}")
            return False
        finally:
            # Always cleanup
            self.cleanup()

def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description="AWS AI Training Pipeline")
    parser.add_argument("--instance-type", default="g5.xlarge", 
                       help="EC2 instance type (default: g5.xlarge)")
    parser.add_argument("--no-spot", action="store_true", 
                       help="Use on-demand instead of spot instances")
    parser.add_argument("--spot-price", default="0.50", 
                       help="Maximum spot price (default: 0.50)")
    
    args = parser.parse_args()
    
    # Create trainer
    trainer = AWSTrainer(
        instance_type=args.instance_type,
        use_spot=not args.no_spot,
        spot_price=args.spot_price
    )
    
    # Run pipeline
    success = trainer.run_complete_pipeline()
    
    if success:
        print("\n🎉 Training completed successfully!")
        print("Your trained model is available in ./trained_model/")
        print(f"Total estimated cost: ~$0.75 (2 hours @ ${args.spot_price}/hour)")
    else:
        print("\n❌ Training failed. Check logs for details.")
        sys.exit(1)

if __name__ == "__main__":
    main()
