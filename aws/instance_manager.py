#!/usr/bin/env python3
"""
AWS EC2 Instance Manager for AI Training
Handles instance lifecycle, spot instances, and cost optimization
"""

import boto3
import time
import json
import logging
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from botocore.exceptions import ClientError

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class InstanceConfig:
    """Configuration for EC2 instance"""
    instance_type: str = "g5.xlarge"
    ami_id: str = "ami-0c02fb55956c7d316"  # Ubuntu 22.04 LTS
    key_name: str = "ai-training-key"
    security_group_ids: List[str] = None
    subnet_id: Optional[str] = None
    use_spot: bool = True
    spot_price: str = "0.50"  # Max price for spot instances
    storage_size: int = 100  # GB
    region: str = "us-east-1"

class AWSInstanceManager:
    """Manages AWS EC2 instances for AI training"""
    
    def __init__(self, config: InstanceConfig):
        self.config = config
        self.ec2_client = boto3.client('ec2', region_name=config.region)
        self.ec2_resource = boto3.resource('ec2', region_name=config.region)
        self.instance_id = None
        self.instance = None
        
    def create_security_group(self) -> str:
        """Create security group for training instances"""
        try:
            # Check if security group already exists
            response = self.ec2_client.describe_security_groups(
                Filters=[
                    {'Name': 'group-name', 'Values': ['ai-training-sg']},
                ]
            )
            
            if response['SecurityGroups']:
                sg_id = response['SecurityGroups'][0]['GroupId']
                logger.info(f"Using existing security group: {sg_id}")
                return sg_id
            
            # Create new security group
            response = self.ec2_client.create_security_group(
                GroupName='ai-training-sg',
                Description='Security group for AI training instances'
            )
            
            sg_id = response['GroupId']
            
            # Add SSH access
            self.ec2_client.authorize_security_group_ingress(
                GroupId=sg_id,
                IpPermissions=[
                    {
                        'IpProtocol': 'tcp',
                        'FromPort': 22,
                        'ToPort': 22,
                        'IpRanges': [{'CidrIp': '0.0.0.0/0'}]
                    }
                ]
            )
            
            logger.info(f"Created security group: {sg_id}")
            return sg_id
            
        except ClientError as e:
            logger.error(f"Error creating security group: {e}")
            raise
    
    def create_key_pair(self) -> str:
        """Create or get existing key pair"""
        try:
            # Check if key pair exists
            response = self.ec2_client.describe_key_pairs(
                KeyNames=[self.config.key_name]
            )
            logger.info(f"Using existing key pair: {self.config.key_name}")
            return self.config.key_name
            
        except ClientError as e:
            if e.response['Error']['Code'] == 'InvalidKeyPair.NotFound':
                # Create new key pair
                response = self.ec2_client.create_key_pair(
                    KeyName=self.config.key_name
                )
                
                # Save private key
                with open(f"{self.config.key_name}.pem", 'w') as f:
                    f.write(response['KeyMaterial'])
                
                logger.info(f"Created new key pair: {self.config.key_name}")
                return self.config.key_name
            else:
                logger.error(f"Error with key pair: {e}")
                raise
    
    def get_latest_deep_learning_ami(self) -> str:
        """Get the latest AWS Deep Learning AMI"""
        try:
            response = self.ec2_client.describe_images(
                Filters=[
                    {'Name': 'name', 'Values': ['Deep Learning AMI GPU PyTorch*']},
                    {'Name': 'state', 'Values': ['available']},
                    {'Name': 'architecture', 'Values': ['x86_64']},
                ],
                Owners=['amazon']
            )
            
            if not response['Images']:
                logger.warning("No Deep Learning AMI found, using default Ubuntu")
                return self.config.ami_id
            
            # Sort by creation date and get the latest
            images = sorted(response['Images'], 
                          key=lambda x: x['CreationDate'], reverse=True)
            
            latest_ami = images[0]['ImageId']
            logger.info(f"Using latest Deep Learning AMI: {latest_ami}")
            return latest_ami
            
        except ClientError as e:
            logger.error(f"Error getting AMI: {e}")
            return self.config.ami_id
    
    def create_user_data_script(self) -> str:
        """Create user data script for instance initialization"""
        return """#!/bin/bash
# Update system
apt-get update -y

# Install Python and pip if not present
apt-get install -y python3 python3-pip git

# Install AWS CLI
pip3 install awscli boto3

# Create training directory
mkdir -p /home/<USER>/training
chown ubuntu:ubuntu /home/<USER>/training

# Install NVIDIA drivers if needed (for non-DL AMI)
if ! command -v nvidia-smi &> /dev/null; then
    apt-get install -y ubuntu-drivers-common
    ubuntu-drivers autoinstall
fi

# Signal that initialization is complete
echo "Instance initialization complete" > /home/<USER>/init_complete.txt
"""
    
    def launch_instance(self) -> str:
        """Launch EC2 instance (spot or on-demand)"""
        try:
            # Setup prerequisites
            if not self.config.security_group_ids:
                sg_id = self.create_security_group()
                self.config.security_group_ids = [sg_id]
            
            self.create_key_pair()
            ami_id = self.get_latest_deep_learning_ami()
            
            # Instance configuration
            instance_config = {
                'ImageId': ami_id,
                'InstanceType': self.config.instance_type,
                'KeyName': self.config.key_name,
                'SecurityGroupIds': self.config.security_group_ids,
                'UserData': self.create_user_data_script(),
                'BlockDeviceMappings': [
                    {
                        'DeviceName': '/dev/sda1',
                        'Ebs': {
                            'VolumeSize': self.config.storage_size,
                            'VolumeType': 'gp3',
                            'DeleteOnTermination': True
                        }
                    }
                ],
                'TagSpecifications': [
                    {
                        'ResourceType': 'instance',
                        'Tags': [
                            {'Key': 'Name', 'Value': 'AI-Training-Instance'},
                            {'Key': 'Purpose', 'Value': 'Qwen3-Training'},
                            {'Key': 'AutoTerminate', 'Value': 'true'}
                        ]
                    }
                ]
            }
            
            if self.config.subnet_id:
                instance_config['SubnetId'] = self.config.subnet_id
            
            if self.config.use_spot:
                # Launch spot instance
                response = self.ec2_client.request_spot_instances(
                    SpotPrice=self.config.spot_price,
                    LaunchSpecification=instance_config,
                    Type='one-time'
                )
                
                spot_request_id = response['SpotInstanceRequests'][0]['SpotInstanceRequestId']
                logger.info(f"Spot instance request created: {spot_request_id}")
                
                # Wait for spot request to be fulfilled
                instance_id = self._wait_for_spot_fulfillment(spot_request_id)
                
            else:
                # Launch on-demand instance
                response = self.ec2_client.run_instances(
                    MinCount=1,
                    MaxCount=1,
                    **instance_config
                )
                
                instance_id = response['Instances'][0]['InstanceId']
                logger.info(f"On-demand instance launched: {instance_id}")
            
            self.instance_id = instance_id
            self.instance = self.ec2_resource.Instance(instance_id)
            
            # Wait for instance to be running
            logger.info("Waiting for instance to be running...")
            self.instance.wait_until_running()
            
            # Get instance details
            self.instance.reload()
            public_ip = self.instance.public_ip_address
            
            logger.info(f"Instance {instance_id} is running at {public_ip}")
            return instance_id
            
        except ClientError as e:
            logger.error(f"Error launching instance: {e}")
            raise
    
    def _wait_for_spot_fulfillment(self, spot_request_id: str, timeout: int = 300) -> str:
        """Wait for spot instance request to be fulfilled"""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            response = self.ec2_client.describe_spot_instance_requests(
                SpotInstanceRequestIds=[spot_request_id]
            )
            
            request = response['SpotInstanceRequests'][0]
            state = request['State']
            
            if state == 'active':
                instance_id = request['InstanceId']
                logger.info(f"Spot request fulfilled: {instance_id}")
                return instance_id
            elif state == 'failed':
                raise Exception(f"Spot request failed: {request.get('Fault', {}).get('Message', 'Unknown error')}")
            
            logger.info(f"Spot request state: {state}, waiting...")
            time.sleep(10)
        
        raise Exception("Timeout waiting for spot instance fulfillment")
    
    def get_instance_info(self) -> Dict:
        """Get current instance information"""
        if not self.instance:
            return {}
        
        self.instance.reload()
        return {
            'instance_id': self.instance.id,
            'state': self.instance.state['Name'],
            'public_ip': self.instance.public_ip_address,
            'private_ip': self.instance.private_ip_address,
            'instance_type': self.instance.instance_type,
            'launch_time': self.instance.launch_time.isoformat() if self.instance.launch_time else None
        }
    
    def terminate_instance(self) -> bool:
        """Terminate the instance"""
        if not self.instance_id:
            logger.warning("No instance to terminate")
            return False
        
        try:
            self.ec2_client.terminate_instances(InstanceIds=[self.instance_id])
            logger.info(f"Terminating instance: {self.instance_id}")
            return True
        except ClientError as e:
            logger.error(f"Error terminating instance: {e}")
            return False
    
    def wait_for_ssh_ready(self, timeout: int = 300) -> bool:
        """Wait for SSH to be ready on the instance"""
        if not self.instance:
            return False
        
        import socket
        
        public_ip = self.instance.public_ip_address
        if not public_ip:
            return False
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(5)
                result = sock.connect_ex((public_ip, 22))
                sock.close()
                
                if result == 0:
                    logger.info("SSH is ready")
                    return True
                    
            except Exception:
                pass
            
            logger.info("Waiting for SSH to be ready...")
            time.sleep(10)
        
        logger.error("Timeout waiting for SSH")
        return False

if __name__ == "__main__":
    # Example usage
    config = InstanceConfig(
        instance_type="g5.xlarge",
        use_spot=True,
        spot_price="0.50"
    )
    
    manager = AWSInstanceManager(config)
    
    try:
        instance_id = manager.launch_instance()
        print(f"Instance launched: {instance_id}")
        
        # Wait for SSH
        if manager.wait_for_ssh_ready():
            print("Instance is ready for SSH connection")
            print(f"Connect with: ssh -i {config.key_name}.pem ubuntu@{manager.instance.public_ip_address}")
        
        # Get instance info
        info = manager.get_instance_info()
        print(f"Instance info: {json.dumps(info, indent=2)}")
        
    except Exception as e:
        print(f"Error: {e}")
        if manager.instance_id:
            manager.terminate_instance()
