#!/usr/bin/env python3
"""
EMERGENCY AWS CLEANUP SCRIPT
Finds and terminates ALL running AWS resources to stop charges
"""

import boto3
import json
import sys
from datetime import datetime

def cleanup_region(region_name):
    """Clean up all resources in a specific region"""
    print(f"\n🔍 Checking region: {region_name}")
    
    try:
        # EC2 Client
        ec2 = boto3.client('ec2', region_name=region_name)
        
        # 1. TERMINATE ALL RUNNING INSTANCES
        print("🚨 Checking for running EC2 instances...")
        instances = ec2.describe_instances(
            Filters=[
                {'Name': 'instance-state-name', 'Values': ['running', 'pending', 'stopping']}
            ]
        )
        
        instance_ids = []
        for reservation in instances['Reservations']:
            for instance in reservation['Instances']:
                instance_id = instance['InstanceId']
                instance_type = instance['InstanceType']
                state = instance['State']['Name']
                launch_time = instance.get('LaunchTime', 'Unknown')
                
                print(f"❌ TERMINATING: {instance_id} ({instance_type}) - {state} - {launch_time}")
                instance_ids.append(instance_id)
        
        if instance_ids:
            ec2.terminate_instances(InstanceIds=instance_ids)
            print(f"✅ Terminated {len(instance_ids)} instances")
        else:
            print("✅ No running instances found")
        
        # 2. CANCEL SPOT REQUESTS
        print("🚨 Checking for active spot requests...")
        spot_requests = ec2.describe_spot_instance_requests(
            Filters=[
                {'Name': 'state', 'Values': ['open', 'active']}
            ]
        )
        
        spot_ids = []
        for request in spot_requests['SpotInstanceRequests']:
            spot_id = request['SpotInstanceRequestId']
            state = request['State']
            instance_type = request['LaunchSpecification']['InstanceType']
            
            print(f"❌ CANCELING SPOT: {spot_id} ({instance_type}) - {state}")
            spot_ids.append(spot_id)
        
        if spot_ids:
            ec2.cancel_spot_instance_requests(SpotInstanceRequestIds=spot_ids)
            print(f"✅ Canceled {len(spot_ids)} spot requests")
        else:
            print("✅ No active spot requests found")
        
        # 3. DELETE LOAD BALANCERS
        try:
            elb = boto3.client('elbv2', region_name=region_name)
            load_balancers = elb.describe_load_balancers()
            
            for lb in load_balancers['LoadBalancers']:
                lb_arn = lb['LoadBalancerArn']
                lb_name = lb['LoadBalancerName']
                print(f"❌ DELETING LOAD BALANCER: {lb_name}")
                elb.delete_load_balancer(LoadBalancerArn=lb_arn)
            
            if load_balancers['LoadBalancers']:
                print(f"✅ Deleted {len(load_balancers['LoadBalancers'])} load balancers")
            else:
                print("✅ No load balancers found")
        except Exception as e:
            print(f"⚠️ Load balancer check failed: {e}")
        
        # 4. DELETE NAT GATEWAYS
        try:
            nat_gateways = ec2.describe_nat_gateways(
                Filters=[
                    {'Name': 'state', 'Values': ['available', 'pending']}
                ]
            )
            
            for nat in nat_gateways['NatGateways']:
                nat_id = nat['NatGatewayId']
                state = nat['State']
                print(f"❌ DELETING NAT GATEWAY: {nat_id} - {state}")
                ec2.delete_nat_gateway(NatGatewayId=nat_id)
            
            if nat_gateways['NatGateways']:
                print(f"✅ Deleted {len(nat_gateways['NatGateways'])} NAT gateways")
            else:
                print("✅ No NAT gateways found")
        except Exception as e:
            print(f"⚠️ NAT gateway check failed: {e}")
        
        # 5. RELEASE ELASTIC IPs
        try:
            addresses = ec2.describe_addresses()
            
            for addr in addresses['Addresses']:
                if 'AssociationId' not in addr:  # Only release unassociated IPs
                    allocation_id = addr.get('AllocationId')
                    public_ip = addr.get('PublicIp')
                    if allocation_id:
                        print(f"❌ RELEASING ELASTIC IP: {public_ip}")
                        ec2.release_address(AllocationId=allocation_id)
            
            unassociated = [a for a in addresses['Addresses'] if 'AssociationId' not in a]
            if unassociated:
                print(f"✅ Released {len(unassociated)} Elastic IPs")
            else:
                print("✅ No unassociated Elastic IPs found")
        except Exception as e:
            print(f"⚠️ Elastic IP check failed: {e}")
        
        # 6. CHECK RDS INSTANCES
        try:
            rds = boto3.client('rds', region_name=region_name)
            db_instances = rds.describe_db_instances()
            
            for db in db_instances['DBInstances']:
                db_id = db['DBInstanceIdentifier']
                db_status = db['DBInstanceStatus']
                db_class = db['DBInstanceClass']
                
                if db_status in ['available', 'creating', 'modifying']:
                    print(f"❌ DELETING RDS: {db_id} ({db_class}) - {db_status}")
                    rds.delete_db_instance(
                        DBInstanceIdentifier=db_id,
                        SkipFinalSnapshot=True,
                        DeleteAutomatedBackups=True
                    )
            
            active_dbs = [db for db in db_instances['DBInstances'] 
                         if db['DBInstanceStatus'] in ['available', 'creating', 'modifying']]
            if active_dbs:
                print(f"✅ Deleted {len(active_dbs)} RDS instances")
            else:
                print("✅ No active RDS instances found")
        except Exception as e:
            print(f"⚠️ RDS check failed: {e}")
        
        # 7. CHECK SAGEMAKER ENDPOINTS
        try:
            sagemaker = boto3.client('sagemaker', region_name=region_name)
            endpoints = sagemaker.list_endpoints(StatusEquals='InService')
            
            for endpoint in endpoints['Endpoints']:
                endpoint_name = endpoint['EndpointName']
                print(f"❌ DELETING SAGEMAKER ENDPOINT: {endpoint_name}")
                sagemaker.delete_endpoint(EndpointName=endpoint_name)
            
            if endpoints['Endpoints']:
                print(f"✅ Deleted {len(endpoints['Endpoints'])} SageMaker endpoints")
            else:
                print("✅ No SageMaker endpoints found")
        except Exception as e:
            print(f"⚠️ SageMaker check failed: {e}")
        
    except Exception as e:
        print(f"❌ Error in region {region_name}: {e}")

def main():
    """Main cleanup function"""
    print("🚨 EMERGENCY AWS CLEANUP - STOPPING ALL CHARGES")
    print("=" * 60)
    
    try:
        # Get account info
        sts = boto3.client('sts')
        identity = sts.get_caller_identity()
        print(f"🔍 Account: {identity['Account']}")
        print(f"🔍 User: {identity['Arn']}")
        
    except Exception as e:
        print(f"❌ Cannot connect to AWS: {e}")
        print("Please run: aws configure")
        sys.exit(1)
    
    # Major regions to check
    regions = [
        'us-east-1',      # N. Virginia (most common)
        'us-west-2',      # Oregon
        'us-west-1',      # N. California
        'eu-west-1',      # Ireland
        'eu-central-1',   # Frankfurt
        'ap-southeast-1', # Singapore
        'ap-northeast-1', # Tokyo
    ]
    
    for region in regions:
        cleanup_region(region)
    
    print("\n" + "=" * 60)
    print("🎉 CLEANUP COMPLETE!")
    print("💰 All billable resources should now be terminated")
    print("⏱️ It may take a few minutes for charges to stop")
    print("\n📊 Next steps:")
    print("1. Check AWS Console to verify everything is stopped")
    print("2. Monitor your billing dashboard")
    print("3. Set up billing alerts to prevent future surprises")

if __name__ == "__main__":
    main()
