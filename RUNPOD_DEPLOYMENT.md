# 🚀 RunPod Combined Dataset Training Deployment

## Quick Start Guide

### 1. Start New RunPod Instance
- **GPU**: A40 or similar (your preference)
- **Template**: PyTorch 2.0+ or CUDA 12.1+
- **Storage**: 50GB+ recommended
- **Ports**: Expose 22 for SSH

### 2. Connect via SSH
```bash
ssh <EMAIL> -p [YOUR_PORT]
```

### 3. Upload Training Package
```bash
# On your local machine
scp -P [YOUR_PORT] runpod_training_package.tar.gz <EMAIL>:/workspace/

# On RunPod instance
cd /workspace
tar -xzf runpod_training_package.tar.gz
chmod +x runpod_deploy.sh
```

### 4. Run Setup Script
```bash
./runpod_deploy.sh
```

### 5. Start Combined Training
```bash
python3 combined_dataset_training.py \
  --output_dir "./qwen3-unified-coder" \
  --max_samples_per_dataset 3000 \
  --num_train_epochs 2 \
  --learning_rate 1e-4 \
  --per_device_train_batch_size 4 \
  --gradient_accumulation_steps 4
```

## What This Solves

✅ **No adapter conflicts** - Single clean training from base model
✅ **Combined datasets** - Python + Frontend + General coding
✅ **Unified model** - One model handles all tasks
✅ **Proper format** - All datasets converted to instruction format

## Training Details

**Datasets Combined:**
- `iamtarun/python_code_instructions_18k_alpaca` (Python focus)
- `HuggingFaceH4/CodeAlpaca_20K` (General coding)
- `marianna13/frontend-instruction-tuning` (Frontend/web)

**Expected Training Time:** ~30-45 minutes on A40
**Expected Model Size:** ~2.8GB (LoRA adapter)
**Memory Usage:** ~35GB VRAM

## Testing the Model

After training, test with:
```bash
python3 test_docker_setup.py  # Verify environment
python3 inference_script.py --model_path "./qwen3-unified-coder" --interactive
```

## Troubleshooting

**If datasets fail to load:**
- Check internet connection
- Reduce `max_samples_per_dataset` to 1000
- Try one dataset at a time

**If VRAM issues:**
- Reduce `per_device_train_batch_size` to 2
- Increase `gradient_accumulation_steps` to 8

**If training is slow:**
- Ensure GPU is being used (check `nvidia-smi`)
- Verify CUDA installation
