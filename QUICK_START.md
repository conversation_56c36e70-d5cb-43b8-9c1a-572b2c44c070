# 🚀 Quick Start Guide - AWS AI Trainer

Get your Qwen3-8B model trained on A<PERSON> in just a few commands!

## 📋 Prerequisites

1. **AWS Account** with appropriate permissions
2. **AWS CLI** configured with your credentials
3. **Python 3.8+** with pip

## ⚡ Quick Setup

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Configure AWS
```bash
aws configure
# Enter your AWS Access Key ID, Secret Key, Region (us-east-1), and output format (json)
```

### 3. Test Setup
```bash
python test_aws_setup.py
```

## 🎯 Start Training

### Option 1: Quick Start (Recommended)
```bash
# Get cost estimate first
python train_aws.py --estimate-only

# Start training with defaults (g5.xlarge spot instance)
python train_aws.py
```

### Option 2: Custom Configuration
```bash
# Use different instance type
python train_aws.py --instance-type g4dn.xlarge

# Use on-demand instead of spot
python train_aws.py --no-spot

# Custom spot price
python train_aws.py --spot-price 0.40
```

## 💰 Cost Estimates

| Instance Type | Spot Price/hr | 2hr Training Cost | Best For |
|---------------|---------------|-------------------|----------|
| **g4dn.xlarge** | ~$0.16 | **~$0.47** | Testing, small models |
| **g5.xlarge** | ~$0.30 | **~$0.75** | **Recommended** |
| **g5.2xlarge** | ~$0.36 | **~$0.87** | Faster training |
| **p3.2xlarge** | ~$0.92 | **~$2.00** | Large models |

*Prices include compute, storage, and data transfer*

## 📊 What Happens During Training

1. **🚀 Instance Launch** - Spot instance with auto-termination
2. **📦 Setup** - Install dependencies and NVIDIA drivers
3. **📥 Data Loading** - Download and combine 3 coding datasets
4. **🧠 Training** - LoRA fine-tuning with 4-bit quantization
5. **📈 Monitoring** - Real-time progress updates
6. **💾 Download** - Automatic model download to local machine
7. **🧹 Cleanup** - Instance termination and cost report

## 🎮 Monitoring Training

The system provides real-time monitoring:
- **System Stats**: CPU, RAM, Disk usage
- **GPU Stats**: Utilization, VRAM, temperature
- **Training Logs**: Recent progress updates
- **Cost Tracking**: Running cost estimates

## 📁 Output Structure

After training, you'll have:
```
./trained_model/
├── adapter_model.safetensors    # LoRA adapter weights
├── adapter_config.json          # LoRA configuration
├── tokenizer.json              # Tokenizer files
├── tokenizer_config.json
├── special_tokens_map.json
└── training_info.json          # Training metadata
```

## 🔧 Using Your Trained Model

```python
from transformers import AutoTokenizer, AutoModelForCausalLM
from peft import PeftModel

# Load base model
base_model = AutoModelForCausalLM.from_pretrained("Qwen/Qwen3-8B")
tokenizer = AutoTokenizer.from_pretrained("Qwen/Qwen3-8B")

# Load your trained adapter
model = PeftModel.from_pretrained(base_model, "./trained_model")

# Generate code
prompt = "Write a Python function to calculate fibonacci numbers:"
inputs = tokenizer(prompt, return_tensors="pt")
outputs = model.generate(**inputs, max_length=200)
print(tokenizer.decode(outputs[0]))
```

## 🛠️ Troubleshooting

### Common Issues

**AWS Credentials Error**
```bash
aws configure list  # Check current config
aws sts get-caller-identity  # Test credentials
```

**Instance Launch Failed**
- Check EC2 service limits in AWS console
- Try different region: `--region us-west-2`
- Use on-demand: `--no-spot`

**Spot Instance Interrupted**
- The system automatically retries with on-demand
- Training state is preserved

**Training Failed**
- Check logs: `cat aws_training.log`
- Instance logs: SSH to instance and check `/home/<USER>/training/`

### Getting Help

1. **Check logs**: `aws_training.log` for detailed information
2. **Test setup**: Run `python test_aws_setup.py`
3. **Cost estimate**: Use `--estimate-only` flag
4. **GitHub Issues**: Report bugs and get community help

## 🎯 Advanced Usage

### Custom Datasets
Edit `aws/training_script_generator.py` to modify the dataset configuration.

### Training Parameters
Modify the `TrainingConfig` class to adjust:
- Learning rate
- Batch size
- Number of epochs
- LoRA parameters

### Cost Optimization
```bash
# Get instance recommendations for budget
python -c "
from aws.cost_optimizer import CostOptimizer
optimizer = CostOptimizer()
recs = optimizer.recommend_instance_type(budget=1.0)
for r in recs:
    print(f'{r[\"instance_type\"]}: ${r[\"spot_cost\"]:.2f}')
"
```

## 🎉 Success!

Once training completes, you'll have:
- ✅ A fine-tuned Qwen3-8B model specialized for Python coding
- ✅ Complete training logs and metrics
- ✅ Cost breakdown and savings report
- ✅ Ready-to-use model files

**Total time**: ~2 hours  
**Total cost**: ~$0.75  
**Savings vs on-demand**: ~70%

Happy training! 🚀
