# 🚀 Kaggle Setup Guide - Qwen3-8B Training

**Complete step-by-step guide to train your Qwen3-8B Python coder on Kaggle for FREE!**

## 🎯 What You'll Get
- ✅ **$0 cost** - Completely free training
- ✅ **Professional model** - Same quality as paid services
- ✅ **2-hour training** - Fast and efficient
- ✅ **30 hours/week** - Generous free GPU quota
- ✅ **No copy/paste** - Just upload and run!

---

## 📋 Prerequisites

1. **Kaggle Account** (free) - Sign up at [kaggle.com](https://kaggle.com)
2. **Phone Verification** - Required for GPU access
3. **The notebook file** - `kaggle_qwen3_training.ipynb` (provided)

---

## 🚀 Step-by-Step Setup

### **Step 1: Create Kaggle Account**
1. Go to [kaggle.com](https://kaggle.com)
2. Click "Register" 
3. Sign up with email or Google account
4. **Verify your phone number** (required for GPU access)

### **Step 2: Enable GPU Access**
1. Go to your **Account Settings**
2. Click **"Phone Verification"**
3. Enter your phone number and verify
4. This unlocks **30 hours/week** of free GPU time!

### **Step 3: Upload the Notebook**
1. Download `kaggle_qwen3_training.ipynb` from this repository
2. Go to [kaggle.com/code](https://kaggle.com/code)
3. Click **"New Notebook"**
4. Click **"File"** → **"Upload Notebook"**
5. Select `kaggle_qwen3_training.ipynb`
6. Click **"Upload"**

### **Step 4: Configure GPU**
1. In the notebook, click **"Settings"** (right panel)
2. Under **"Accelerator"**, select **"GPU P100"** or **"GPU T4"**
3. Under **"Internet"**, make sure it's **"On"** (needed to download datasets)
4. Click **"Save"**

### **Step 5: Start Training**
1. Click **"Run All"** at the top
2. Confirm you want to run all cells
3. **That's it!** The notebook will:
   - Install all dependencies
   - Download and combine datasets
   - Load and configure Qwen3-8B
   - Train the model with LoRA
   - Save and package your trained model

---

## ⏱️ What to Expect

### **Timeline:**
- **Setup**: 5-10 minutes (installing packages)
- **Data Loading**: 10-15 minutes (downloading datasets)
- **Model Loading**: 5-10 minutes (loading Qwen3-8B)
- **Training**: 1-2 hours (the main event!)
- **Testing & Packaging**: 5-10 minutes

### **Progress Indicators:**
You'll see real-time updates showing:
- ✅ Package installation progress
- ✅ Dataset download and processing
- ✅ Model loading with memory usage
- ✅ Training progress with loss metrics
- ✅ Model testing with example outputs
- ✅ Final packaging for download

### **Training Output:**
```
🏋️ Training configuration complete!
📁 Output directory: /kaggle/working/qwen3-python-coder
🎯 Training samples: 5,700
📊 Evaluation samples: 300
⚡ Batch size: 4
🔄 Gradient accumulation: 4
📈 Learning rate: 0.0002
🔁 Epochs: 2

🚀 Starting training...
⏱️ This will take approximately 1-2 hours
📊 You'll see progress updates below
```

---

## 📥 Download Your Model

### **After Training Completes:**
1. Scroll to the bottom of the notebook
2. You'll see: **"📦 Download your model: qwen3-python-coder-trained.tar.gz"**
3. In the **"Output"** tab (right panel), click the download icon
4. Download both files:
   - `qwen3-python-coder-trained.tar.gz` (your model)
   - `USAGE_INSTRUCTIONS.txt` (how to use it)

### **File Sizes:**
- **Model package**: ~150-200 MB
- **Instructions**: ~5 KB

---

## 🚀 Using Your Trained Model

### **Extract the Model:**
```bash
tar -xzf qwen3-python-coder-trained.tar.gz
```

### **Load and Use:**
```python
from transformers import AutoTokenizer, AutoModelForCausalLM
from peft import PeftModel

# Load base model
base_model = AutoModelForCausalLM.from_pretrained("Qwen/Qwen3-8B")
tokenizer = AutoTokenizer.from_pretrained("Qwen/Qwen3-8B")

# Load your trained adapter
model = PeftModel.from_pretrained(base_model, "./qwen3-python-coder")

# Generate code
prompt = "Write a Python function to calculate fibonacci numbers:"
formatted_prompt = f"<|im_start|>user\n{prompt}<|im_end|>\n<|im_start|>assistant\n"
inputs = tokenizer(formatted_prompt, return_tensors="pt")
outputs = model.generate(**inputs, max_length=200)
response = tokenizer.decode(outputs[0], skip_special_tokens=True)
print(response)
```

---

## 🛠️ Troubleshooting

### **Common Issues:**

**"GPU quota exceeded"**
- You've used your 30 hours/week
- Wait for quota reset (weekly)
- Or use CPU (much slower)

**"Phone verification required"**
- Go to Account Settings → Phone Verification
- This is required for GPU access

**"Internet connection required"**
- Enable "Internet" in notebook settings
- Needed to download datasets and models

**"Out of memory error"**
- Restart the notebook: Kernel → Restart
- Try again - sometimes memory gets fragmented

**"Package installation failed"**
- Restart and run again
- Kaggle sometimes has temporary package issues

### **Getting Help:**
1. **Check the notebook output** - errors are usually clearly shown
2. **Restart the notebook** - fixes most memory/package issues
3. **Try different GPU** - P100 vs T4 have different memory
4. **Check Kaggle status** - [status.kaggle.com](https://status.kaggle.com)

---

## 💡 Pro Tips

### **Maximize Success:**
1. **Run during off-peak hours** - Less competition for GPUs
2. **Don't close the browser** - Keep the tab open during training
3. **Monitor progress** - Check back every 30 minutes
4. **Save intermediate results** - The notebook auto-saves checkpoints

### **GPU Quota Management:**
- **30 hours/week** resets every Monday
- **Check usage**: Settings → Account → GPU Quota
- **Plan training**: 2 hours per model = 15 models/week possible

### **Dataset Alternatives:**
If a dataset fails to load, the notebook will skip it and continue with others. You'll still get a great model!

---

## 🎉 Success Indicators

### **You'll know it worked when you see:**
```
🎉 TRAINING COMPLETE! 🎉
📦 Download your model: qwen3-python-coder-trained.tar.gz
📋 Read instructions: USAGE_INSTRUCTIONS.txt
💾 Package size: 180.5 MB
⏱️ Total time: 1:45:23

🚀 Your Qwen3-8B Python Coder is ready to use!
```

### **Your Model Will Be Able To:**
- ✅ Generate Python code from natural language
- ✅ Debug and fix existing code  
- ✅ Explain code functionality
- ✅ Handle web development tasks
- ✅ Solve programming problems

---

## 🆚 Kaggle vs Other Platforms

| Feature | Kaggle | AWS | Google Colab | RunPod |
|---------|--------|-----|--------------|--------|
| **Cost** | **FREE** | $0.75+ | $10/month | $0.50/hour |
| **Setup** | **1-click** | Complex | Medium | Medium |
| **GPU Time** | **30h/week** | Unlimited | Limited | Unlimited |
| **Billing Risk** | **None** | High | Medium | Medium |
| **AI Help** | **95%** | 95% | 95% | 70% |

**Winner: Kaggle** - Free, simple, and no billing surprises!

---

## 🎯 Ready to Start?

1. **Download**: `kaggle_qwen3_training.ipynb`
2. **Upload**: To Kaggle Notebooks
3. **Configure**: GPU enabled
4. **Run**: Click "Run All"
5. **Wait**: 2 hours for magic to happen
6. **Download**: Your trained model

**Total effort**: 5 minutes of setup + 2 hours of automated training = **Professional AI coding assistant!**

Happy training! 🚀🐍✨
