#!/usr/bin/env python3
"""
Dataset Explorer - Check available datasets and their formats
"""

from datasets import load_dataset
import json

def explore_dataset(dataset_name, max_samples=3):
    """Explore a dataset structure and show samples"""
    print(f"\n{'='*60}")
    print(f"📊 EXPLORING: {dataset_name}")
    print(f"{'='*60}")
    
    try:
        # Load dataset
        dataset = load_dataset(dataset_name, split="train")
        print(f"📈 Total samples: {len(dataset)}")
        print(f"🔧 Columns: {dataset.column_names}")
        
        # Show samples
        for i in range(min(max_samples, len(dataset))):
            print(f"\n--- Sample {i+1} ---")
            sample = dataset[i]
            for key, value in sample.items():
                if isinstance(value, str):
                    # Truncate long strings
                    display_value = value[:200] + "..." if len(value) > 200 else value
                    print(f"{key}: {repr(display_value)}")
                else:
                    print(f"{key}: {type(value)} - {str(value)[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Error loading {dataset_name}: {e}")
        return False

def main():
    """Explore recommended datasets"""
    
    # Recommended datasets for combined training
    datasets_to_explore = [
        # Python coding datasets
        "iamtarun/python_code_instructions_18k_alpaca",
        "HuggingFaceH4/CodeAlpaca_20K",
        "sahil2801/CodeAlpaca-20k",
        
        # Frontend/Web datasets  
        "marianna13/frontend-instruction-tuning",
        
        # General code datasets
        "nickrosh/Evol-Instruct-Code-80k-v1",
        "TokenBender/code_instructions_122k_alpaca_style",
    ]
    
    print("🔍 DATASET EXPLORATION REPORT")
    print("="*60)
    
    successful_datasets = []
    failed_datasets = []
    
    for dataset_name in datasets_to_explore:
        success = explore_dataset(dataset_name)
        if success:
            successful_datasets.append(dataset_name)
        else:
            failed_datasets.append(dataset_name)
    
    # Summary
    print(f"\n{'='*60}")
    print("📋 SUMMARY")
    print(f"{'='*60}")
    print(f"✅ Successfully loaded: {len(successful_datasets)}")
    for dataset in successful_datasets:
        print(f"   - {dataset}")
    
    if failed_datasets:
        print(f"\n❌ Failed to load: {len(failed_datasets)}")
        for dataset in failed_datasets:
            print(f"   - {dataset}")
    
    print(f"\n💡 RECOMMENDATION:")
    print("For combined training, use these successful datasets:")
    for dataset in successful_datasets[:3]:  # Top 3
        print(f"   - {dataset}")

if __name__ == "__main__":
    main()
