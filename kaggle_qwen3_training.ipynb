# Install packages
%%capture
!pip install -q transformers==4.45.0 datasets==2.14.0 peft==0.12.0
!pip install -q bitsandbytes==0.41.0 accelerate==0.25.0 scipy
print('✅ Packages installed!')

# Setup and imports
import os
import torch
import json
from datetime import datetime
from transformers import *
from datasets import Dataset, load_dataset
from peft import LoraConfig, get_peft_model, TaskType

device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f'🎮 Device: {device}')
if torch.cuda.is_available():
    print(f'🚀 GPU: {torch.cuda.get_device_name(0)}')
    print(f'💾 Memory: {torch.cuda.get_device_properties(0).total_memory/1e9:.1f}GB')

# Load datasets
print('📥 Loading datasets...')
datasets_config = [
    {'name': 'iamtarun/python_code_instructions_18k_alpaca', 'samples': 2000},
    {'name': 'sahil2801/CodeAlpaca-20k', 'samples': 2000},
    {'name': 'flytech/python-codes-25k', 'samples': 2000}
]

all_data = []
for config in datasets_config:
    try:
        print(f'📂 Loading {config["name"]}...')
        ds = load_dataset(config['name'], split='train')
        if len(ds) > config['samples']:
            ds = ds.shuffle(seed=42).select(range(config['samples']))
        
        for item in ds:
            try:
                if 'instruction' in item and 'output' in item:
                    inst = item['instruction']
                    resp = item['output']
                    inp = item.get('input', '')
                elif 'prompt' in item and 'completion' in item:
                    inst = item['prompt']
                    resp = item['completion']
                    inp = ''
                else:
                    continue
                
                if len(resp) < 10 or len(resp) > 2000:
                    continue
                
                full_inst = f'{inst}\n\nInput: {inp}' if inp else inst
                all_data.append({'instruction': full_inst, 'response': resp})
            except:
                continue
        
        print(f'✅ Loaded {config["name"]}')
    except Exception as e:
        print(f'⚠️ Failed {config["name"]}: {e}')

dataset = Dataset.from_list(all_data)
print(f'🎯 Total samples: {len(dataset)}')

# Setup model
print('🧠 Loading Qwen3-8B...')
model_name = 'Qwen/Qwen3-8B'

bnb_config = BitsAndBytesConfig(
    load_in_4bit=True,
    bnb_4bit_compute_dtype=torch.float16,
    bnb_4bit_quant_type='nf4',
    bnb_4bit_use_double_quant=True
)

tokenizer = AutoTokenizer.from_pretrained(model_name, trust_remote_code=True)
if tokenizer.pad_token is None:
    tokenizer.pad_token = tokenizer.eos_token

model = AutoModelForCausalLM.from_pretrained(
    model_name,
    quantization_config=bnb_config,
    device_map='auto',
    trust_remote_code=True,
    torch_dtype=torch.float16
)

lora_config = LoraConfig(
    task_type=TaskType.CAUSAL_LM,
    r=16, lora_alpha=32, lora_dropout=0.1,
    target_modules=['q_proj', 'k_proj', 'v_proj', 'o_proj', 'gate_proj', 'up_proj', 'down_proj']
)

model = get_peft_model(model, lora_config)
model.print_trainable_parameters()
print('✅ Model ready!')

# Tokenize data
print('🔤 Tokenizing...')
def tokenize_fn(examples):
    texts = []
    for inst, resp in zip(examples['instruction'], examples['response']):
        text = f'<|im_start|>user\n{inst}<|im_end|>\n<|im_start|>assistant\n{resp}<|im_end|>'
        texts.append(text)
    
    tokenized = tokenizer(texts, truncation=True, padding=False, max_length=2048)
    tokenized['labels'] = tokenized['input_ids'].copy()
    return tokenized

tokenized_dataset = dataset.map(tokenize_fn, batched=True, remove_columns=dataset.column_names)

train_size = int(0.95 * len(tokenized_dataset))
train_dataset = tokenized_dataset.select(range(train_size))
eval_dataset = tokenized_dataset.select(range(train_size, len(tokenized_dataset)))

print(f'📊 Train: {len(train_dataset)}, Eval: {len(eval_dataset)}')

# Training setup
output_dir = '/kaggle/working/qwen3-python-coder'
os.makedirs(output_dir, exist_ok=True)

training_args = TrainingArguments(
    output_dir=output_dir,
    num_train_epochs=2,
    per_device_train_batch_size=4,
    per_device_eval_batch_size=4,
    gradient_accumulation_steps=4,
    learning_rate=2e-4,
    warmup_steps=100,
    logging_steps=10,
    save_steps=500,
    eval_steps=500,
    evaluation_strategy='steps',
    save_strategy='steps',
    load_best_model_at_end=True,
    fp16=True,
    dataloader_pin_memory=False,
    remove_unused_columns=False,
    report_to=None,
    save_total_limit=2
)

trainer = Trainer(
    model=model,
    args=training_args,
    train_dataset=train_dataset,
    eval_dataset=eval_dataset,
    data_collator=DataCollatorForLanguageModeling(tokenizer=tokenizer, mlm=False)
)

print('🏋️ Training setup complete!')

# Start training
print('🚀 Starting training...')
print('⏱️ This will take ~2 hours')
start_time = datetime.now()

trainer.train()

end_time = datetime.now()
duration = end_time - start_time
print(f'🎉 Training complete! Time: {str(duration).split(".")[0]}')

# Save model
print('💾 Saving model...')
trainer.save_model()
tokenizer.save_pretrained(output_dir)

# Save info
info = {
    'model': 'Qwen/Qwen3-8B',
    'method': 'LoRA',
    'samples': len(train_dataset),
    'time': str(duration).split('.')[0],
    'completed': datetime.now().isoformat()
}
with open(f'{output_dir}/info.json', 'w') as f:
    json.dump(info, f, indent=2)

print('✅ Model saved!')

# Test model
print('🧪 Testing model...')
def test_model(prompt):
    text = f'<|im_start|>user\n{prompt}<|im_end|>\n<|im_start|>assistant\n'
    inputs = tokenizer(text, return_tensors='pt').to(model.device)
    with torch.no_grad():
        outputs = model.generate(**inputs, max_length=inputs.input_ids.shape[1]+150, temperature=0.7, do_sample=True)
    response = tokenizer.decode(outputs[0], skip_special_tokens=True)
    return response.split('<|im_start|>assistant\n')[-1]

prompts = [
    'Write a Python function to calculate factorial',
    'Create a simple calculator class',
    'How to read a CSV file in Python?'
]

for i, prompt in enumerate(prompts, 1):
    print(f'\n📝 Test {i}: {prompt}')
    try:
        response = test_model(prompt)
        print(f'🤖 Response: {response[:200]}...')
    except Exception as e:
        print(f'❌ Error: {e}')

print('\n✅ Testing complete!')

# Create download package
import tarfile
print('📦 Creating download package...')

package_path = '/kaggle/working/qwen3-python-coder.tar.gz'
with tarfile.open(package_path, 'w:gz') as tar:
    tar.add(output_dir, arcname='qwen3-python-coder')

size_mb = os.path.getsize(package_path) / (1024*1024)

# Usage instructions
instructions = f'''
# 🎉 Your Qwen3-8B Python Coder is Ready!

## Download and Extract:
tar -xzf qwen3-python-coder.tar.gz

## Usage:
```python
from transformers import AutoTokenizer, AutoModelForCausalLM
from peft import PeftModel

base_model = AutoModelForCausalLM.from_pretrained("Qwen/Qwen3-8B")
tokenizer = AutoTokenizer.from_pretrained("Qwen/Qwen3-8B")
model = PeftModel.from_pretrained(base_model, "./qwen3-python-coder")

# Generate code
prompt = "Write a Python function to sort a list:"
text = f"<|im_start|>user\\n{{prompt}}<|im_end|>\\n<|im_start|>assistant\\n"
inputs = tokenizer(text, return_tensors="pt")
outputs = model.generate(**inputs, max_length=200)
print(tokenizer.decode(outputs[0], skip_special_tokens=True))
```

Training completed in {str(duration).split(".")[0]} with {len(train_dataset)} samples.
Enjoy your custom Python coding assistant! 🚀
'''

with open('/kaggle/working/USAGE.txt', 'w') as f:
    f.write(instructions)

print('\n' + '='*50)
print('🎉 TRAINING COMPLETE! 🎉')
print('='*50)
print(f'📦 Download: qwen3-python-coder.tar.gz ({size_mb:.1f}MB)')
print(f'📋 Instructions: USAGE.txt')
print(f'⏱️ Time: {str(duration).split(".")[0]}')
print('🚀 Your Qwen3-8B Python Coder is ready!')
print('\nFiles in Output tab: ⬇️')
for f in os.listdir('/kaggle/working'):
    if f.endswith(('.tar.gz', '.txt')):
        print(f'  📄 {f}')
print('\n🎯 Happy coding! 🐍✨')