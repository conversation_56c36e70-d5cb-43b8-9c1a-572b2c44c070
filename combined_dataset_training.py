#!/usr/bin/env python3
"""
Combined Dataset Training Script
Combines multiple datasets for comprehensive code training
"""

import os
import torch
import logging
import argparse
from datasets import load_dataset, concatenate_datasets, Dataset
from transformers import (
    AutoTokenizer,
    AutoModelForCausalLM,
    BitsAndBytesConfig,
    TrainingArguments,
    Trainer,
    DataCollatorForLanguageModeling,
)
from peft import LoraConfig, get_peft_model, TaskType
import json

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def load_and_format_dataset(dataset_name, split="train", max_samples=None):
    """Load and format a dataset to standard instruction format"""
    logger.info(f"Loading dataset: {dataset_name}")
    
    try:
        dataset = load_dataset(dataset_name, split=split)
        if max_samples:
            dataset = dataset.select(range(min(max_samples, len(dataset))))
        
        # Format based on dataset structure
        if dataset_name == "iamtarun/python_code_instructions_18k_alpaca":
            # Already in instruction format
            return dataset
            
        elif dataset_name == "marianna13/frontend-instruction-tuning":
            # Convert to instruction format
            def format_frontend(example):
                # This dataset has images + JSON, we'll focus on the JSON part
                if 'json' in example and example['json']:
                    json_data = example['json'] if isinstance(example['json'], dict) else {}
                    html = json_data.get('html', 'None')
                    css = json_data.get('css', 'None')
                    
                    if html != 'None' or css != 'None':
                        instruction = "Create a web page with the following requirements:"
                        if html != 'None':
                            instruction += f" HTML: {html[:200]}..."
                        if css != 'None':
                            instruction += f" CSS: {css[:200]}..."
                        
                        response = ""
                        if html != 'None':
                            response += f"HTML:\n{html}\n\n"
                        if css != 'None':
                            response += f"CSS:\n{css}"
                        
                        return {
                            "instruction": instruction,
                            "input": "",
                            "output": response
                        }
                return None
            
            formatted = dataset.map(format_frontend, remove_columns=dataset.column_names)
            formatted = formatted.filter(lambda x: x is not None)
            return formatted
            
        elif dataset_name == "HuggingFaceH4/CodeAlpaca_20K":
            # Already in instruction format, just rename columns if needed
            def rename_columns(example):
                return {
                    "instruction": example.get("instruction", ""),
                    "input": example.get("input", ""),
                    "output": example.get("output", "")
                }
            return dataset.map(rename_columns)
            
        else:
            logger.warning(f"Unknown dataset format for {dataset_name}, skipping...")
            return None
            
    except Exception as e:
        logger.error(f"Failed to load {dataset_name}: {e}")
        return None

def combine_datasets(dataset_configs):
    """Combine multiple datasets into one"""
    datasets = []
    
    for config in dataset_configs:
        dataset = load_and_format_dataset(**config)
        if dataset:
            datasets.append(dataset)
            logger.info(f"Added {len(dataset)} samples from {config['dataset_name']}")
    
    if not datasets:
        raise ValueError("No datasets were successfully loaded!")
    
    combined = concatenate_datasets(datasets)
    logger.info(f"Combined dataset size: {len(combined)} samples")
    return combined

def format_prompt(instruction, input_text="", output=""):
    """Format data into training prompt"""
    if input_text.strip():
        prompt = f"### Instruction:\n{instruction}\n\n### Input:\n{input_text}\n\n### Response:\n{output}"
    else:
        prompt = f"### Instruction:\n{instruction}\n\n### Response:\n{output}"
    return prompt

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--output_dir", default="./qwen3-unified-coder", help="Output directory")
    parser.add_argument("--model_name", default="Qwen/Qwen3-8B", help="Base model")
    parser.add_argument("--max_samples_per_dataset", type=int, default=5000, help="Max samples per dataset")
    parser.add_argument("--num_train_epochs", type=int, default=2, help="Training epochs")
    parser.add_argument("--learning_rate", type=float, default=1e-4, help="Learning rate")
    parser.add_argument("--per_device_train_batch_size", type=int, default=4, help="Batch size")
    parser.add_argument("--gradient_accumulation_steps", type=int, default=4, help="Gradient accumulation")
    
    args = parser.parse_args()
    
    # Dataset configurations
    dataset_configs = [
        {
            "dataset_name": "iamtarun/python_code_instructions_18k_alpaca",
            "max_samples": args.max_samples_per_dataset
        },
        {
            "dataset_name": "marianna13/frontend-instruction-tuning", 
            "max_samples": args.max_samples_per_dataset
        },
        {
            "dataset_name": "HuggingFaceH4/CodeAlpaca_20K",
            "max_samples": args.max_samples_per_dataset
        }
    ]
    
    # Load and combine datasets
    logger.info("🔄 Loading and combining datasets...")
    combined_dataset = combine_datasets(dataset_configs)
    
    # Format for training
    def format_for_training(example):
        formatted_text = format_prompt(
            example["instruction"], 
            example.get("input", ""), 
            example["output"]
        )
        return {"text": formatted_text}
    
    train_dataset = combined_dataset.map(format_for_training)
    
    # Load model and tokenizer
    logger.info(f"🤖 Loading model: {args.model_name}")
    tokenizer = AutoTokenizer.from_pretrained(args.model_name)
    tokenizer.pad_token = tokenizer.eos_token
    
    # Quantization config
    bnb_config = BitsAndBytesConfig(
        load_in_4bit=True,
        bnb_4bit_use_double_quant=True,
        bnb_4bit_quant_type="nf4",
        bnb_4bit_compute_dtype=torch.bfloat16,
    )
    
    model = AutoModelForCausalLM.from_pretrained(
        args.model_name,
        quantization_config=bnb_config,
        device_map="auto",
        trust_remote_code=True,
    )
    
    # LoRA configuration
    lora_config = LoraConfig(
        task_type=TaskType.CAUSAL_LM,
        r=32,
        lora_alpha=64,
        lora_dropout=0.1,
        target_modules=["q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"],
    )
    
    model = get_peft_model(model, lora_config)
    model.print_trainable_parameters()
    
    # Tokenize dataset
    def tokenize_function(examples):
        return tokenizer(examples["text"], truncation=True, padding=False, max_length=1024)
    
    tokenized_dataset = train_dataset.map(tokenize_function, batched=True, remove_columns=["text"])
    
    # Training arguments
    training_args = TrainingArguments(
        output_dir=args.output_dir,
        num_train_epochs=args.num_train_epochs,
        per_device_train_batch_size=args.per_device_train_batch_size,
        gradient_accumulation_steps=args.gradient_accumulation_steps,
        learning_rate=args.learning_rate,
        logging_steps=10,
        save_steps=500,
        save_total_limit=2,
        remove_unused_columns=False,
        dataloader_drop_last=True,
        gradient_checkpointing=True,
    )
    
    # Data collator
    data_collator = DataCollatorForLanguageModeling(tokenizer=tokenizer, mlm=False)
    
    # Trainer
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=tokenized_dataset,
        data_collator=data_collator,
    )
    
    # Train
    logger.info("🚀 Starting training...")
    trainer.train()
    
    # Save
    logger.info("💾 Saving model...")
    trainer.save_model()
    tokenizer.save_pretrained(args.output_dir)
    
    logger.info("✅ Training completed!")

if __name__ == "__main__":
    main()
