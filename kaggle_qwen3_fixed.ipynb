{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 🚀 Qwen3-8B Python Coder Training (Fixed Version)\n", "\n", "**Fixed for Kaggle GPU and dataset issues**\n", "\n", "## ⚠️ IMPORTANT: Enable GPU First!\n", "\n", "**Before running this notebook:**\n", "1. Click **Settings** (gear icon) on the right panel\n", "2. Under **Accelerator**, select **GPU T4 x2** or **GPU P100**\n", "3. Under **Internet**, make sure it's **ON**\n", "4. Click **Save**\n", "\n", "If you don't see GPU options, verify your phone number in Kaggle account settings.\n", "\n", "---\n", "\n", "- 💰 Cost: **$0** (100% Free on Kaggle)\n", "- ⏱️ Time: ~2 hours with GPU\n", "- 🎮 GPU: **REQUIRED** - T4 or P100\n", "- 📊 Datasets: Reliable coding datasets\n", "- 🧠 Model: Qwen3-8B with LoRA fine-tuning"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check GPU availability first\n", "import torch\n", "import subprocess\n", "import sys\n", "\n", "print('🔍 Checking GPU availability...')\n", "if torch.cuda.is_available():\n", "    print(f'✅ GPU detected: {torch.cuda.get_device_name(0)}')\n", "    print(f'💾 GPU Memory: {torch.cuda.get_device_properties(0).total_memory/1e9:.1f}GB')\n", "    gpu_available = True\n", "else:\n", "    print('❌ NO GPU DETECTED!')\n", "    print('⚠️ Training will be extremely slow without GPU')\n", "    print('\\n🔧 To enable GPU:')\n", "    print('1. <PERSON><PERSON> (gear icon) on the right')\n", "    print('2. Under Accelerator, select GPU T4 x2 or GPU P100')\n", "    print('3. Under Internet, make sure it is ON')\n", "    print('4. Click Save and restart the notebook')\n", "    gpu_available = False\n", "\n", "# Ask user if they want to continue without GPU\n", "if not gpu_available:\n", "    print('\\n❓ Continue without GPU? (Training will take 10+ hours)')\n", "    print('Recommendation: Enable GPU first!')\n", "    # We'll continue but warn the user"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Install packages with better error handling\n", "packages = [\n", "    'transformers==4.45.0',\n", "    'datasets==2.14.0', \n", "    'peft==0.12.0',\n", "    'bitsandbytes==0.41.0',\n", "    'accelerate==0.25.0',\n", "    'scipy'\n", "]\n", "\n", "print('📦 Installing packages...')\n", "for package in packages:\n", "    try:\n", "        print(f'Installing {package}...')\n", "        result = subprocess.run(\n", "            [sys.executable, '-m', 'pip', 'install', '-q', package],\n", "            capture_output=True,\n", "            text=True,\n", "            timeout=300\n", "        )\n", "        if result.returncode == 0:\n", "            print(f'✅ {package}')\n", "        else:\n", "            print(f'⚠️ Warning installing {package}: {result.stderr}')\n", "    except Exception as e:\n", "        print(f'❌ Failed {package}: {e}')\n", "\n", "print('✅ Package installation complete!')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Setup and imports\n", "import os\n", "import json\n", "from datetime import datetime\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Import ML libraries\n", "try:\n", "    from transformers import (\n", "        AutoTokenizer, AutoModelForCausalLM, BitsAndBytesConfig,\n", "        TrainingArguments, Trainer, DataCollatorForLanguageModeling\n", "    )\n", "    from datasets import Dataset, load_dataset\n", "    from peft import LoraConfig, get_peft_model, TaskType\n", "    print('✅ All libraries imported successfully')\n", "except ImportError as e:\n", "    print(f'❌ Import error: {e}')\n", "    print('Please restart the notebook and try again')\n", "    raise\n", "\n", "# Set device\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "print(f'🎮 Using device: {device}')\n", "\n", "print('✅ Setup complete!')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load datasets with better error handling and fallbacks\n", "print('📥 Loading datasets...')\n", "\n", "all_data = []\n", "\n", "# Try multiple datasets with fallbacks\n", "dataset_configs = [\n", "    {\n", "        'name': 'iam<PERSON>un/python_code_instructions_18k_alpaca',\n", "        'split': 'train',\n", "        'max_samples': 2000,\n", "        'format': 'alpaca'\n", "    },\n", "    {\n", "        'name': 'sahil2801/CodeAlpaca-20k', \n", "        'split': 'train',\n", "        'max_samples': 2000,\n", "        'format': 'alpaca'\n", "    },\n", "    {\n", "        'name': 'nickrosh/Evol-Instruct-Code-80k-v1',\n", "        'split': 'train',\n", "        'max_samples': 1000,\n", "        'format': 'alpaca'\n", "    },\n", "    {\n", "        'name': 'codeparrot/github-code-clean',\n", "        'split': 'train',\n", "        'max_samples': 1000,\n", "        'format': 'code',\n", "        'config': '<PERSON>'\n", "    }\n", "]\n", "\n", "for config in dataset_configs:\n", "    try:\n", "        print(f'📂 Trying {config[\"name\"]}...')\n", "        \n", "        # Load dataset with different methods\n", "        if 'config' in config:\n", "            try:\n", "                ds = load_dataset(config['name'], config['config'], split=config['split'], streaming=False)\n", "            except:\n", "                ds = load_dataset(config['name'], split=config['split'], streaming=False)\n", "        else:\n", "            ds = load_dataset(config['name'], split=config['split'], streaming=False)\n", "        \n", "        # Sample data\n", "        max_samples = min(config['max_samples'], len(ds))\n", "        if len(ds) > max_samples:\n", "            ds = ds.shuffle(seed=42).select(range(max_samples))\n", "        \n", "        # Process based on format\n", "        start_len = len(all_data)\n", "        \n", "        for item in ds:\n", "            try:\n", "                if config['format'] == 'alpaca':\n", "                    if 'instruction' in item and 'output' in item:\n", "                        inst = str(item['instruction']).strip()\n", "                        resp = str(item['output']).strip()\n", "                        \n", "                        if 10 <= len(resp) <= 2000 and len(inst) > 5:\n", "                            all_data.append({\n", "                                'instruction': inst,\n", "                                'response': resp\n", "                            })\n", "                            \n", "                elif config['format'] == 'code':\n", "                    if 'code' in item:\n", "                        code = str(item['code']).strip()\n", "                        if 50 <= len(code) <= 2000 and ('def ' in code or 'class ' in code):\n", "                            all_data.append({\n", "                                'instruction': 'Write Python code for the following task:',\n", "                                'response': code\n", "                            })\n", "            except Exception as e:\n", "                continue\n", "        \n", "        added = len(all_data) - start_len\n", "        print(f'✅ Added {added} samples from {config[\"name\"]}')\n", "        \n", "        # Stop if we have enough data\n", "        if len(all_data) >= 3000:\n", "            break\n", "            \n", "    except Exception as e:\n", "        print(f'⚠️ Failed to load {config[\"name\"]}: {e}')\n", "        continue\n", "\n", "# Fallback: Create synthetic data if needed\n", "if len(all_data) < 500:\n", "    print('⚠️ Not enough data from datasets, creating synthetic examples...')\n", "    \n", "    synthetic_examples = [\n", "        {\n", "            'instruction': 'Write a Python function to calculate factorial',\n", "            'response': 'def factorial(n):\\n    if n == 0 or n == 1:\\n        return 1\\n    else:\\n        return n * factorial(n - 1)'\n", "        },\n", "        {\n", "            'instruction': 'Create a function to check if a number is prime',\n", "            'response': 'def is_prime(n):\\n    if n < 2:\\n        return False\\n    for i in range(2, int(n**0.5) + 1):\\n        if n % i == 0:\\n            return False\\n    return True'\n", "        },\n", "        {\n", "            'instruction': 'Write a function to reverse a string',\n", "            'response': 'def reverse_string(s):\\n    return s[::-1]'\n", "        }\n", "    ]\n", "    \n", "    # Repeat synthetic examples to get enough data\n", "    while len(all_data) < 500:\n", "        all_data.extend(synthetic_examples)\n", "    \n", "    all_data = all_data[:500]  # Limit to 500 for quick training\n", "\n", "print(f'🎯 Total training samples: {len(all_data)}')\n", "\n", "if len(all_data) < 10:\n", "    raise ValueError('Insufficient training data - please check internet connection')\n", "\n", "# Create dataset\n", "dataset = Dataset.from_list(all_data)\n", "print('✅ Dataset created successfully!')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Setup model with better error handling\n", "print('🧠 Loading Qwen3-8B...')\n", "model_name = 'Qwen/Qwen3-8B'\n", "\n", "try:\n", "    # Quantization config (only if GPU available)\n", "    if torch.cuda.is_available():\n", "        bnb_config = BitsAndBytesConfig(\n", "            load_in_4bit=True,\n", "            bnb_4bit_compute_dtype=torch.float16,\n", "            bnb_4bit_quant_type='nf4',\n", "            bnb_4bit_use_double_quant=True\n", "        )\n", "        print('✅ 4-bit quantization enabled')\n", "    else:\n", "        bnb_config = None\n", "        print('⚠️ No quantization (CPU mode)')\n", "    \n", "    # Load tokenizer\n", "    print('📝 Loading tokenizer...')\n", "    tokenizer = AutoTokenizer.from_pretrained(model_name, trust_remote_code=True)\n", "    if tokenizer.pad_token is None:\n", "        tokenizer.pad_token = tokenizer.eos_token\n", "    print('✅ Tokenizer loaded')\n", "    \n", "    # Load model\n", "    print('🚀 Loading model (this may take a few minutes)...')\n", "    model_kwargs = {\n", "        'trust_remote_code': True,\n", "        'torch_dtype': torch.float16 if torch.cuda.is_available() else torch.float32\n", "    }\n", "    \n", "    if bnb_config:\n", "        model_kwargs['quantization_config'] = bnb_config\n", "        model_kwargs['device_map'] = 'auto'\n", "    \n", "    model = AutoModelForCausalLM.from_pretrained(model_name, **model_kwargs)\n", "    print('✅ Model loaded')\n", "    \n", "    # Setup LoRA\n", "    print('🔧 Setting up LoRA...')\n", "    lora_config = LoraConfig(\n", "        task_type=TaskType.CAUSAL_LM,\n", "        r=16,\n", "        lora_alpha=32,\n", "        lora_dropout=0.1,\n", "        target_modules=['q_proj', 'k_proj', 'v_proj', 'o_proj', 'gate_proj', 'up_proj', 'down_proj']\n", "    )\n", "    \n", "    model = get_peft_model(model, lora_config)\n", "    model.print_trainable_parameters()\n", "    print('✅ LoRA setup complete')\n", "    \n", "except Exception as e:\n", "    print(f'❌ Error loading model: {e}')\n", "    print('This might be due to:')\n", "    print('1. Insufficient GPU memory')\n", "    print('2. Network issues downloading the model')\n", "    print('3. Missing GPU acceleration')\n", "    raise\n", "\n", "print('✅ Model setup complete!')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Tokenize data\n", "print('🔤 Tokenizing dataset...')\n", "\n", "def tokenize_function(examples):\n", "    texts = []\n", "    for inst, resp in zip(examples['instruction'], examples['response']):\n", "        # Use Qwen3 chat format\n", "        text = f'<|im_start|>user\\n{inst}<|im_end|>\\n<|im_start|>assistant\\n{resp}<|im_end|>'\n", "        texts.append(text)\n", "    \n", "    # Tokenize with appropriate max length\n", "    max_length = 1024 if not torch.cuda.is_available() else 2048\n", "    tokenized = tokenizer(\n", "        texts,\n", "        truncation=True,\n", "        padding=False,\n", "        max_length=max_length,\n", "        return_tensors=None\n", "    )\n", "    \n", "    tokenized['labels'] = tokenized['input_ids'].copy()\n", "    return tokenized\n", "\n", "# Tokenize dataset\n", "try:\n", "    tokenized_dataset = dataset.map(\n", "        tokenize_function,\n", "        batched=True,\n", "        remove_columns=dataset.column_names,\n", "        desc='Tokenizing'\n", "    )\n", "    \n", "    # Split train/eval\n", "    train_size = int(0.95 * len(tokenized_dataset))\n", "    train_dataset = tokenized_dataset.select(range(train_size))\n", "    eval_dataset = tokenized_dataset.select(range(train_size, len(tokenized_dataset)))\n", "    \n", "    print(f'📊 Train samples: {len(train_dataset)}')\n", "    print(f'📊 Eval samples: {len(eval_dataset)}')\n", "    print('✅ Tokenization complete')\n", "    \n", "except Exception as e:\n", "    print(f'❌ Tokenization error: {e}')\n", "    raise"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Training setup with adaptive parameters\n", "print('🏋️ Setting up training...')\n", "\n", "# Output directory\n", "output_dir = './qwen3-python-coder'\n", "os.makedirs(output_dir, exist_ok=True)\n", "\n", "# Adaptive training parameters based on available resources\n", "if torch.cuda.is_available():\n", "    # GPU settings\n", "    batch_size = 4\n", "    gradient_accumulation = 4\n", "    epochs = 2\n", "    fp16 = True\n", "    print('🚀 Using GPU-optimized settings')\n", "else:\n", "    # CPU settings (much smaller)\n", "    batch_size = 1\n", "    gradient_accumulation = 1\n", "    epochs = 1\n", "    fp16 = False\n", "    print('⚠️ Using CPU settings (will be slow)')\n", "\n", "# Training arguments\n", "training_args = TrainingArguments(\n", "    output_dir=output_dir,\n", "    num_train_epochs=epochs,\n", "    per_device_train_batch_size=batch_size,\n", "    per_device_eval_batch_size=batch_size,\n", "    gradient_accumulation_steps=gradient_accumulation,\n", "    learning_rate=2e-4,\n", "    warmup_steps=50,\n", "    logging_steps=10,\n", "    save_steps=200,\n", "    eval_steps=200,\n", "    evaluation_strategy='steps',\n", "    save_strategy='steps',\n", "    load_best_model_at_end=True,\n", "    fp16=fp16,\n", "    dataloader_pin_memory=False,\n", "    remove_unused_columns=False,\n", "    report_to=None,\n", "    save_total_limit=2,\n", "    max_steps=500 if len(train_dataset) > 1000 else -1  # Limit steps for large datasets\n", ")\n", "\n", "# Data collator\n", "data_collator = DataCollatorForLanguageModeling(\n", "    tokenizer=tokenizer,\n", "    mlm=False\n", ")\n", "\n", "# Create trainer\n", "trainer = Trainer(\n", "    model=model,\n", "    args=training_args,\n", "    train_dataset=train_dataset,\n", "    eval_dataset=eval_dataset,\n", "    data_collator=data_collator\n", ")\n", "\n", "print('✅ Training setup complete!')\n", "print(f'📊 Training samples: {len(train_dataset)}')\n", "print(f'📊 Batch size: {batch_size}')\n", "print(f'📊 Epochs: {epochs}')\n", "print(f'📊 Estimated time: {\"30-60 min\" if torch.cuda.is_available() else \"3-6 hours\"}')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Start training with progress monitoring\n", "print('🚀 Starting training...')\n", "print(f'⏱️ Estimated time: {\"30-60 minutes\" if torch.cuda.is_available() else \"3-6 hours\"}')\n", "print('📊 You will see progress updates below')\n", "print('\\n' + '='*50)\n", "\n", "start_time = datetime.now()\n", "print(f'🕐 Training started at: {start_time.strftime(\"%H:%M:%S\")}')\n", "\n", "try:\n", "    # Train the model\n", "    trainer.train()\n", "    \n", "    end_time = datetime.now()\n", "    duration = end_time - start_time\n", "    \n", "    print('\\n' + '='*50)\n", "    print(f'🎉 Training completed at: {end_time.strftime(\"%H:%M:%S\")}')\n", "    print(f'⏱️ Total training time: {str(duration).split(\".\")[0]}')\n", "    print('✅ Training successful!')\n", "    \n", "except Exception as e:\n", "    print(f'\\n❌ Training failed: {e}')\n", "    print('\\nPossible solutions:')\n", "    print('1. <PERSON>art notebook and enable GPU')\n", "    print('2. Reduce batch size if out of memory')\n", "    print('3. Check internet connection')\n", "    raise"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Save model and create package\n", "print('💾 Saving trained model...')\n", "\n", "try:\n", "    # Save model and tokenizer\n", "    trainer.save_model()\n", "    tokenizer.save_pretrained(output_dir)\n", "    \n", "    # Save training info\n", "    training_info = {\n", "        'model_name': 'Qwen/Qwen3-8B',\n", "        'training_method': 'LoRA fine-tuning',\n", "        'total_samples': len(train_dataset) + len(eval_dataset),\n", "        'train_samples': len(train_dataset),\n", "        'eval_samples': len(eval_dataset),\n", "        'training_time': str(duration).split('.')[0],\n", "        'completion_time': datetime.now().isoformat(),\n", "        'gpu_used': torch.cuda.is_available(),\n", "        'device': str(device)\n", "    }\n", "    \n", "    with open(f'{output_dir}/training_info.json', 'w') as f:\n", "        json.dump(training_info, f, indent=2)\n", "    \n", "    print(f'✅ Model saved to: {output_dir}')\n", "    print('🎯 Your trained model is ready!')\n", "    \n", "    # List saved files\n", "    print('\\n📁 Saved files:')\n", "    for file in os.listdir(output_dir):\n", "        file_path = os.path.join(output_dir, file)\n", "        if os.path.isfile(file_path):\n", "            size_mb = os.path.getsize(file_path) / (1024 * 1024)\n", "            print(f'  📄 {file} ({size_mb:.1f} MB)')\n", "    \n", "except Exception as e:\n", "    print(f'❌ Error saving model: {e}')\n", "    raise"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test the trained model\n", "print('🧪 Testing your trained model...')\n", "\n", "def test_model(prompt, max_length=150):\n", "    \"\"\"Test the model with a coding prompt\"\"\"\n", "    try:\n", "        formatted_prompt = f'<|im_start|>user\\n{prompt}<|im_end|>\\n<|im_start|>assistant\\n'\n", "        \n", "        inputs = tokenizer(formatted_prompt, return_tensors='pt')\n", "        if torch.cuda.is_available():\n", "            inputs = inputs.to(model.device)\n", "        \n", "        with torch.no_grad():\n", "            outputs = model.generate(\n", "                **inputs,\n", "                max_length=inputs.input_ids.shape[1] + max_length,\n", "                temperature=0.7,\n", "                do_sample=True,\n", "                pad_token_id=tokenizer.eos_token_id,\n", "                eos_token_id=tokenizer.eos_token_id\n", "            )\n", "        \n", "        response = tokenizer.decode(outputs[0], skip_special_tokens=True)\n", "        \n", "        # Extract assistant response\n", "        if '<|im_start|>assistant\\n' in response:\n", "            response = response.split('<|im_start|>assistant\\n')[-1]\n", "        \n", "        return response.strip()\n", "        \n", "    except Exception as e:\n", "        return f'Error generating response: {e}'\n", "\n", "# Test prompts\n", "test_prompts = [\n", "    'Write a Python function to calculate factorial of a number',\n", "    'Create a simple class for a calculator',\n", "    'How do I read a CSV file in Python?'\n", "]\n", "\n", "print('\\n🔍 Testing with sample prompts:')\n", "for i, prompt in enumerate(test_prompts, 1):\n", "    print(f'\\n📝 Test {i}: {prompt}')\n", "    print('🤖 Response:')\n", "    \n", "    response = test_model(prompt)\n", "    # Truncate long responses for display\n", "    if len(response) > 300:\n", "        response = response[:300] + '...'\n", "    print(response)\n", "    print('-' * 50)\n", "\n", "print('\\n✅ Model testing complete!')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create final summary and usage instructions\n", "print('📋 Creating usage instructions...')\n", "\n", "usage_instructions = f'''\n", "# 🎉 Your Qwen3-8B Python Coder is Ready!\n", "\n", "## 📊 Training Summary:\n", "- Model: Qwen3-8B with LoRA fine-tuning\n", "- Training samples: {len(train_dataset):,}\n", "- Training time: {str(duration).split(\".\")[0]}\n", "- Device used: {\"GPU\" if torch.cuda.is_available() else \"CPU\"}\n", "- Completion: {datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\")}\n", "\n", "## 🚀 How to Use Your Model:\n", "\n", "### Option 1: In this notebook\n", "```python\n", "# Your model is already loaded as 'model' and 'tokenizer'\n", "prompt = \"Write a Python function to sort a list\"\n", "response = test_model(prompt)\n", "print(response)\n", "```\n", "\n", "### Option 2: Load in new environment\n", "```python\n", "from transformers import AutoTokenizer, AutoModelForCausalLM\n", "from peft import PeftModel\n", "\n", "# Load base model\n", "base_model = AutoModelForCausalLM.from_pretrained(\"Qwen/Qwen3-8B\")\n", "tokenizer = AutoTokenizer.from_pretrained(\"Qwen/Qwen3-8B\")\n", "\n", "# Load your trained adapter\n", "model = PeftModel.from_pretrained(base_model, \"./qwen3-python-coder\")\n", "\n", "# Generate code\n", "prompt = \"Write a Python function to calculate fibonacci:\"\n", "text = f\"<|im_start|>user\\\\n{{prompt}}<|im_end|>\\\\n<|im_start|>assistant\\\\n\"\n", "inputs = tokenizer(text, return_tensors=\"pt\")\n", "outputs = model.generate(**inputs, max_length=200)\n", "response = tokenizer.decode(outputs[0], skip_special_tokens=True)\n", "print(response)\n", "```\n", "\n", "## 🎯 What Your Model Can Do:\n", "✅ Generate Python code from natural language descriptions\n", "✅ Debug and fix existing code\n", "✅ Explain code functionality\n", "✅ Handle programming problems\n", "✅ Provide coding best practices\n", "\n", "## 📁 Files Created:\n", "- qwen3-python-coder/ (model directory)\n", "- training_info.json (training details)\n", "- This usage guide\n", "\n", "Enjoy your custom-trained Python coding assistant! 🚀\n", "'''\n", "\n", "# Save instructions\n", "with open('USAGE_INSTRUCTIONS.txt', 'w') as f:\n", "    f.write(usage_instructions)\n", "\n", "# Final summary\n", "print('\\n' + '='*60)\n", "print('🎉 TRAINING COMPLETE! 🎉')\n", "print('='*60)\n", "print(f'📁 Model directory: {output_dir}/')\n", "print(f'📋 Usage guide: USAGE_INSTRUCTIONS.txt')\n", "print(f'⏱️ Training time: {str(duration).split(\".\")[0]}')\n", "print(f'📊 Training samples: {len(train_dataset):,}')\n", "print(f'🎮 Device used: {\"GPU\" if torch.cuda.is_available() else \"CPU\"}')\n", "print('\\n🚀 Your Qwen3-8B Python Coder is ready!')\n", "print('\\n📁 Files available:')\n", "print(f'  📂 {output_dir}/ (trained model)')\n", "print('  📄 USAGE_INSTRUCTIONS.txt')\n", "print('  📄 training_info.json')\n", "print('\\n🎯 Happy coding with your AI assistant! 🐍✨')\n", "\n", "# Show model info\n", "if torch.cuda.is_available():\n", "    print(f'\\n💾 GPU Memory used: {torch.cuda.max_memory_allocated()/1e9:.1f}GB')\n", "    print(f'💾 GPU Memory available: {torch.cuda.get_device_properties(0).total_memory/1e9:.1f}GB')"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}