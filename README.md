# AWS AI Trainer - Qwen3-8B Python Coder

A fully automated AWS-based training pipeline for fine-tuning Qwen3-8B on Python code instructions with complete AI assistance.

## 🎯 Overview

This repository provides a **fully automated AWS pipeline** for:
- **Training**: Fine-tune Qwen3-8B on combined coding datasets using QLoRA
- **Automation**: Complete AWS EC2 instance management via AI assistant
- **Cost Optimization**: Spot instances and auto-termination for minimal costs
- **Evaluation**: Comprehensive scoring system and code sandbox for testing

### Key Features
- **Model**: Qwen3-8B (latest Qwen family model)
- **Datasets**: Combined Python, web dev, and general coding instructions
- **Method**: LoRA/QLoRA for parameter-efficient fine-tuning
- **Platform**: AWS EC2 with full programmatic control
- **Cost**: ~$0.75 total training cost using spot instances
- **Framework**: HuggingFace Transformers + PEFT + AWS SDK

## 📁 Project Structure

```
aitraining/
├── README.md                    # This file
├── requirements.txt             # Python dependencies
├── config_manager.py           # Configuration management
├── scoring_system.py           # Evaluation and scoring
├── code_sandbox.py             # Safe code execution environment
└── aws/                        # AWS automation scripts (generated)
    ├── aws_trainer.py          # Main AWS training automation
    ├── instance_manager.py     # EC2 instance management
    └── cost_optimizer.py       # Spot instance and cost optimization
```

## 🚀 Quick Start

### 1. Prerequisites

```bash
# Install AWS CLI and configure credentials
pip install awscli boto3
aws configure  # Enter your AWS credentials
```

### 2. AI-Assisted Setup

The AI assistant will handle everything automatically:
- AWS EC2 instance creation and management
- Training script deployment and execution
- Cost optimization with spot instances
- Automatic model download upon completion

### 3. Start Training

```bash
# The AI assistant will create and run the complete pipeline
# Total cost: ~$0.75 for full training
# Time: ~2 hours for complete training
```

## 💰 Cost Breakdown

| Component | Cost | Description |
|-----------|------|-------------|
| **g5.xlarge spot** | $0.60 | 2 hours @ $0.30/hour |
| **Storage** | $0.10 | EBS storage for training |
| **Data Transfer** | $0.05 | Model download |
| **Total** | **$0.75** | Complete training cost |

## 🔧 AWS Instance Options

| Instance Type | GPU | VRAM | Price/Hour | Best For |
|---------------|-----|------|------------|----------|
| **g4dn.xlarge** | T4 | 16GB | $0.526 | Testing |
| **g5.xlarge** | A10G | 24GB | $1.006 | Recommended |
| **p3.2xlarge** | V100 | 16GB | $3.060 | Large models |

## 🎯 Training Configuration

### Model & Dataset
- **Base Model**: Qwen/Qwen3-8B
- **Training Method**: LoRA (Low-Rank Adaptation)
- **Datasets**: 
  - Python code instructions (2,000 samples)
  - Web development tasks (2,000 samples)
  - General coding problems (2,000 samples)
- **Total Samples**: 6,000 diverse coding examples

### Training Parameters
- **Epochs**: 2
- **Batch Size**: 4 (with gradient accumulation)
- **Learning Rate**: 2e-4
- **LoRA Rank**: 16
- **Quantization**: 4-bit (QLoRA)

## 🔍 Evaluation & Testing

### Scoring System
- **Code Quality**: Syntax, style, best practices
- **Functionality**: Correctness and efficiency
- **Documentation**: Comments and docstrings
- **Security**: Safe coding practices

### Code Sandbox
- **Safe Execution**: Isolated environment for testing
- **Multiple Languages**: Python, JavaScript, etc.
- **Performance Metrics**: Execution time and memory usage

## 🚀 Advanced Features

### Automated Pipeline
- **Instance Management**: Auto-launch and terminate
- **Cost Optimization**: Spot instances with fallback
- **Progress Monitoring**: Real-time training updates
- **Error Handling**: Automatic retry and recovery

### Model Management
- **Automatic Upload**: S3 storage for trained models
- **Version Control**: Model versioning and tracking
- **Easy Deployment**: One-click inference setup

## 📊 Expected Results

After training, your model will be able to:
- ✅ Generate Python code from natural language descriptions
- ✅ Debug and fix existing code
- ✅ Explain code functionality and best practices
- ✅ Handle web development tasks (HTML, CSS, JavaScript)
- ✅ Solve general programming problems

## 🛠️ Troubleshooting

### Common Issues
- **AWS Credentials**: Ensure proper IAM permissions
- **Instance Limits**: Check EC2 service quotas
- **Spot Interruption**: Automatic fallback to on-demand

### Support
- **AI Assistant**: Full automation and troubleshooting
- **Documentation**: Comprehensive guides and examples
- **Community**: GitHub issues and discussions

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- **Qwen Team**: For the excellent Qwen3-8B model
- **HuggingFace**: For the transformers library and datasets
- **AWS**: For reliable cloud infrastructure
- **Community**: For datasets and feedback

---

**Ready to start training? Let the AI assistant handle everything automatically!**
