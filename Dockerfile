# AI Training Docker Environment
FROM nvidia/cuda:12.1-devel-ubuntu22.04

# Set environment variables
ENV DEBIAN_FRONTEND=noninteractive
ENV PYTHONUNBUFFERED=1
ENV CUDA_HOME=/usr/local/cuda
ENV PATH=${CUDA_HOME}/bin:${PATH}
ENV LD_LIBRARY_PATH=${CUDA_HOME}/lib64:${LD_LIBRARY_PATH}

# Install system dependencies
RUN apt-get update && apt-get install -y \
    python3 \
    python3-pip \
    python3-dev \
    git \
    wget \
    curl \
    vim \
    htop \
    tmux \
    && rm -rf /var/lib/apt/lists/*

# Upgrade pip
RUN python3 -m pip install --upgrade pip

# Install PyTorch with CUDA support
RUN pip3 install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121

# Install transformers and training dependencies
RUN pip3 install \
    transformers>=4.36.0 \
    datasets \
    accelerate \
    peft \
    bitsandbytes \
    scipy \
    scikit-learn \
    wandb \
    tensorboard \
    jupyter \
    ipywidgets

# Set working directory
WORKDIR /workspace

# Copy training scripts
COPY . /workspace/

# Make scripts executable
RUN chmod +x *.py *.sh 2>/dev/null || true

# Create directories
RUN mkdir -p /workspace/models /workspace/datasets /workspace/outputs

# Set default command
CMD ["/bin/bash"]
