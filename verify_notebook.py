#!/usr/bin/env python3
"""
Notebook Verification Script
Checks that the Kaggle notebook has all required components
"""

import json
import sys
from pathlib import Path

def verify_notebook(notebook_path):
    """Verify the notebook structure and content"""
    print("🔍 Verifying Kaggle notebook...")
    
    try:
        with open(notebook_path, 'r') as f:
            notebook = json.load(f)
    except Exception as e:
        print(f"❌ Error loading notebook: {e}")
        return False
    
    # Check basic structure
    if 'cells' not in notebook:
        print("❌ No cells found in notebook")
        return False
    
    cells = notebook['cells']
    print(f"📊 Found {len(cells)} cells")
    
    # Required sections
    required_sections = [
        "Install Dependencies",
        "Setup and Configuration", 
        "Load and Combine Datasets",
        "Setup Model and Tokenizer",
        "Tokenize Dataset",
        "Training Configuration",
        "Start Training",
        "Save the Trained Model",
        "Test Your Trained Model",
        "Create Download Package"
    ]
    
    found_sections = []
    code_cells = 0
    markdown_cells = 0
    
    for cell in cells:
        if cell['cell_type'] == 'markdown':
            markdown_cells += 1
            # Check for section headers
            source = ''.join(cell['source'])
            for section in required_sections:
                if section in source:
                    found_sections.append(section)
                    
        elif cell['cell_type'] == 'code':
            code_cells += 1
    
    print(f"📝 Markdown cells: {markdown_cells}")
    print(f"💻 Code cells: {code_cells}")
    
    # Check required sections
    print("\n🔍 Checking required sections:")
    missing_sections = []
    for section in required_sections:
        if section in found_sections:
            print(f"✅ {section}")
        else:
            print(f"❌ {section}")
            missing_sections.append(section)
    
    # Check for key imports and functions
    all_code = ""
    for cell in cells:
        if cell['cell_type'] == 'code':
            all_code += ''.join(cell['source']) + "\n"
    
    print("\n🔍 Checking key components:")
    
    key_components = {
        "transformers import": "from transformers import",
        "datasets import": "from datasets import", 
        "peft import": "from peft import",
        "torch import": "import torch",
        "BitsAndBytesConfig": "BitsAndBytesConfig",
        "LoraConfig": "LoraConfig",
        "Trainer": "Trainer",
        "load_dataset": "load_dataset",
        "AutoModelForCausalLM": "AutoModelForCausalLM",
        "AutoTokenizer": "AutoTokenizer"
    }
    
    missing_components = []
    for component, search_term in key_components.items():
        if search_term in all_code:
            print(f"✅ {component}")
        else:
            print(f"❌ {component}")
            missing_components.append(component)
    
    # Check for error handling
    print("\n🔍 Checking error handling:")
    error_handling_patterns = [
        "try:",
        "except",
        "Exception",
        "continue"
    ]
    
    has_error_handling = any(pattern in all_code for pattern in error_handling_patterns)
    if has_error_handling:
        print("✅ Error handling present")
    else:
        print("⚠️ Limited error handling")
    
    # Summary
    print("\n" + "="*50)
    print("📋 VERIFICATION SUMMARY")
    print("="*50)
    
    if not missing_sections and not missing_components:
        print("🎉 Notebook verification PASSED!")
        print("✅ All required sections present")
        print("✅ All key components included")
        print("✅ Ready for Kaggle upload")
        return True
    else:
        print("⚠️ Notebook verification FAILED!")
        if missing_sections:
            print(f"❌ Missing sections: {', '.join(missing_sections)}")
        if missing_components:
            print(f"❌ Missing components: {', '.join(missing_components)}")
        return False

def main():
    """Main verification function"""
    notebook_path = "kaggle_qwen3_training.ipynb"
    
    if not Path(notebook_path).exists():
        print(f"❌ Notebook not found: {notebook_path}")
        sys.exit(1)
    
    success = verify_notebook(notebook_path)
    
    if success:
        print("\n🚀 Next steps:")
        print("1. Upload kaggle_qwen3_training.ipynb to Kaggle")
        print("2. Enable GPU in notebook settings")
        print("3. Click 'Run All' and wait for training")
        print("4. Download your trained model!")
        sys.exit(0)
    else:
        print("\n🔧 Please fix the issues above before uploading")
        sys.exit(1)

if __name__ == "__main__":
    main()
