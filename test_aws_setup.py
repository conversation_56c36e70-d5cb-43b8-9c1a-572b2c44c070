#!/usr/bin/env python3
"""
Test AWS Setup and Configuration
Verify that all components are working correctly
"""

import os
import sys
import json
import logging
from pathlib import Path

# Add aws directory to path
sys.path.append(str(Path(__file__).parent / "aws"))

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_imports():
    """Test that all required modules can be imported"""
    print("🔍 Testing imports...")
    
    try:
        import boto3
        print("✅ boto3 imported successfully")
    except ImportError as e:
        print(f"❌ boto3 import failed: {e}")
        return False
    
    try:
        from aws.instance_manager import AWSInstanceManager, InstanceConfig
        print("✅ instance_manager imported successfully")
    except ImportError as e:
        print(f"❌ instance_manager import failed: {e}")
        return False
    
    try:
        from aws.training_script_generator import TrainingScriptGenerator, TrainingConfig
        print("✅ training_script_generator imported successfully")
    except ImportError as e:
        print(f"❌ training_script_generator import failed: {e}")
        return False
    
    try:
        from aws.cost_optimizer import CostOptimizer
        print("✅ cost_optimizer imported successfully")
    except ImportError as e:
        print(f"❌ cost_optimizer import failed: {e}")
        return False
    
    try:
        from aws.aws_trainer import AWSTrainer
        print("✅ aws_trainer imported successfully")
    except ImportError as e:
        print(f"❌ aws_trainer import failed: {e}")
        return False
    
    return True

def test_aws_credentials():
    """Test AWS credentials"""
    print("\n🔑 Testing AWS credentials...")
    
    try:
        import boto3
        sts = boto3.client('sts')
        identity = sts.get_caller_identity()
        
        print(f"✅ AWS credentials verified")
        print(f"   Account ID: {identity['Account']}")
        print(f"   User ARN: {identity['Arn']}")
        return True
        
    except Exception as e:
        print(f"❌ AWS credentials test failed: {e}")
        print("   Please run: aws configure")
        return False

def test_script_generation():
    """Test training script generation"""
    print("\n📝 Testing script generation...")
    
    try:
        from aws.training_script_generator import TrainingScriptGenerator, TrainingConfig
        
        config = TrainingConfig()
        generator = TrainingScriptGenerator(config)
        
        # Test generating scripts
        test_dir = "/tmp/test_scripts"
        output_dir = generator.generate_all_files(test_dir)
        
        # Check if files were created
        expected_files = ["requirements.txt", "train_qwen3.py", "setup.sh"]
        for filename in expected_files:
            filepath = Path(test_dir) / filename
            if filepath.exists():
                print(f"✅ {filename} generated successfully")
            else:
                print(f"❌ {filename} not generated")
                return False
        
        print(f"✅ All training scripts generated in: {output_dir}")
        return True
        
    except Exception as e:
        print(f"❌ Script generation test failed: {e}")
        return False

def test_cost_optimizer():
    """Test cost optimization features"""
    print("\n💰 Testing cost optimizer...")
    
    try:
        from aws.cost_optimizer import CostOptimizer
        
        optimizer = CostOptimizer()
        
        # Test price retrieval
        instance_type = "g5.xlarge"
        on_demand_price = optimizer.get_on_demand_price(instance_type)
        spot_price = optimizer.get_current_spot_price(instance_type)
        
        print(f"✅ On-demand price for {instance_type}: ${on_demand_price:.3f}/hour")
        print(f"✅ Current spot price for {instance_type}: ${spot_price:.3f}/hour")
        
        # Test recommendations
        recommendations = optimizer.recommend_instance_type(budget=1.0)
        print(f"✅ Generated {len(recommendations)} instance recommendations")
        
        return True
        
    except Exception as e:
        print(f"❌ Cost optimizer test failed: {e}")
        return False

def test_instance_config():
    """Test instance configuration"""
    print("\n⚙️  Testing instance configuration...")
    
    try:
        from aws.instance_manager import InstanceConfig
        
        config = InstanceConfig(
            instance_type="g5.xlarge",
            use_spot=True,
            spot_price="0.50"
        )
        
        print(f"✅ Instance config created:")
        print(f"   Type: {config.instance_type}")
        print(f"   Spot: {config.use_spot}")
        print(f"   Price: ${config.spot_price}")
        
        return True
        
    except Exception as e:
        print(f"❌ Instance config test failed: {e}")
        return False

def test_main_trainer():
    """Test main trainer initialization"""
    print("\n🚀 Testing main trainer...")
    
    try:
        from aws.aws_trainer import AWSTrainer
        
        trainer = AWSTrainer(
            instance_type="g5.xlarge",
            use_spot=True,
            spot_price="0.50"
        )
        
        print("✅ AWSTrainer initialized successfully")
        print(f"   Instance type: {trainer.instance_config.instance_type}")
        print(f"   Use spot: {trainer.instance_config.use_spot}")
        
        # Test script generation
        script_dir = trainer.generate_training_files()
        print(f"✅ Training files generated: {script_dir}")
        
        return True
        
    except Exception as e:
        print(f"❌ Main trainer test failed: {e}")
        return False

def run_all_tests():
    """Run all tests"""
    print("🧪 AWS AI Trainer - Setup Test Suite")
    print("=" * 50)
    
    tests = [
        ("Import Test", test_imports),
        ("AWS Credentials", test_aws_credentials),
        ("Script Generation", test_script_generation),
        ("Cost Optimizer", test_cost_optimizer),
        ("Instance Config", test_instance_config),
        ("Main Trainer", test_main_trainer),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"\n⚠️  {test_name} failed")
        except Exception as e:
            print(f"\n💥 {test_name} crashed: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Your AWS setup is ready.")
        print("\nNext steps:")
        print("1. Run: python train_aws.py --estimate-only")
        print("2. Run: python train_aws.py (to start training)")
        return True
    else:
        print("❌ Some tests failed. Please fix the issues above.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
