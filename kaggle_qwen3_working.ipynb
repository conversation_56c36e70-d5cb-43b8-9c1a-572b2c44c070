{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 🚀 Qwen3-8B Python Coder Training (Kaggle Working Version)\n", "\n", "**Fixed for <PERSON><PERSON>'s file system limitations**\n", "\n", "## ⚠️ IMPORTANT: Enable GPU First!\n", "\n", "**Before running:**\n", "1. Click **Settings** (gear icon) on the right\n", "2. **Accelerator** → **GPU T4 x2** or **GPU P100**\n", "3. **Internet** → **ON**\n", "4. Click **Save**\n", "\n", "---\n", "\n", "- 💰 Cost: **$0** (100% Free)\n", "- ⏱️ Time: ~30-60 minutes with GPU\n", "- 🎮 GPU: **REQUIRED** for reasonable speed\n", "- 📊 Datasets: Built-in coding examples\n", "- 🧠 Model: Qwen3-8B with LoRA"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check GPU and install packages\n", "import torch\n", "import subprocess\n", "import sys\n", "\n", "print('🔍 Checking GPU...')\n", "if torch.cuda.is_available():\n", "    print(f'✅ GPU: {torch.cuda.get_device_name(0)}')\n", "    print(f'💾 Memory: {torch.cuda.get_device_properties(0).total_memory/1e9:.1f}GB')\n", "else:\n", "    print('❌ NO GPU! Enable GPU in Settings → Accelerator → GPU T4')\n", "    print('Training without GPU will take 6+ hours!')\n", "\n", "# Install packages\n", "packages = ['transformers==4.45.0', 'datasets==2.14.0', 'peft==0.12.0', \n", "           'bitsandbytes==0.41.0', 'accelerate==0.25.0', 'scipy']\n", "\n", "print('\\n📦 Installing packages...')\n", "for pkg in packages:\n", "    try:\n", "        subprocess.run([sys.executable, '-m', 'pip', 'install', '-q', pkg], \n", "                      check=True, capture_output=True)\n", "        print(f'✅ {pkg.split(\"==\")[0]}')\n", "    except:\n", "        print(f'⚠️ {pkg.split(\"==\")[0]} (may already be installed)')\n", "\n", "print('✅ Setup complete!')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import libraries\n", "import os\n", "import json\n", "from datetime import datetime\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "from transformers import (\n", "    AutoTokenizer, AutoModelForCausalLM, BitsAndBytesConfig,\n", "    TrainingArguments, Trainer, DataCollatorForLanguageModeling\n", ")\n", "from datasets import Dataset\n", "from peft import LoraConfig, get_peft_model, TaskType\n", "\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "print(f'🎮 Device: {device}')\n", "print('✅ Libraries imported!')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create training dataset (avoiding Kaggle file system issues)\n", "print('📊 Creating training dataset...')\n", "\n", "# Built-in Python coding examples (no external downloads needed)\n", "training_examples = [\n", "    {\n", "        'instruction': 'Write a Python function to calculate the factorial of a number',\n", "        'response': '''def factorial(n):\n", "    if n == 0 or n == 1:\n", "        return 1\n", "    else:\n", "        return n * factorial(n - 1)\n", "\n", "# Example usage\n", "print(factorial(5))  # Output: 120'''\n", "    },\n", "    {\n", "        'instruction': 'Create a Python function to check if a number is prime',\n", "        'response': '''def is_prime(n):\n", "    if n < 2:\n", "        return False\n", "    for i in range(2, int(n**0.5) + 1):\n", "        if n % i == 0:\n", "            return False\n", "    return True\n", "\n", "# Example usage\n", "print(is_prime(17))  # Output: True'''\n", "    },\n", "    {\n", "        'instruction': 'Write a function to reverse a string',\n", "        'response': '''def reverse_string(s):\n", "    return s[::-1]\n", "\n", "# Alternative method\n", "def reverse_string_loop(s):\n", "    result = \"\"\n", "    for char in s:\n", "        result = char + result\n", "    return result\n", "\n", "# Example usage\n", "print(reverse_string(\"hello\"))  # Output: \"olleh\"'''\n", "    },\n", "    {\n", "        'instruction': 'Create a simple calculator class in Python',\n", "        'response': '''class Calculator:\n", "    def add(self, a, b):\n", "        return a + b\n", "    \n", "    def subtract(self, a, b):\n", "        return a - b\n", "    \n", "    def multiply(self, a, b):\n", "        return a * b\n", "    \n", "    def divide(self, a, b):\n", "        if b != 0:\n", "            return a / b\n", "        else:\n", "            return \"Error: Division by zero\"\n", "\n", "# Example usage\n", "calc = Calculator()\n", "print(calc.add(5, 3))  # Output: 8'''\n", "    },\n", "    {\n", "        'instruction': 'Write a function to find the largest element in a list',\n", "        'response': '''def find_largest(lst):\n", "    if not lst:\n", "        return None\n", "    \n", "    largest = lst[0]\n", "    for num in lst[1:]:\n", "        if num > largest:\n", "            largest = num\n", "    return largest\n", "\n", "# Using built-in function\n", "def find_largest_builtin(lst):\n", "    return max(lst) if lst else None\n", "\n", "# Example usage\n", "numbers = [3, 7, 2, 9, 1]\n", "print(find_largest(numbers))  # Output: 9'''\n", "    },\n", "    {\n", "        'instruction': 'How to read a CSV file in Python',\n", "        'response': '''import csv\n", "\n", "# Method 1: Using csv module\n", "def read_csv_basic(filename):\n", "    with open(filename, 'r') as file:\n", "        csv_reader = csv.reader(file)\n", "        for row in csv_reader:\n", "            print(row)\n", "\n", "# Method 2: Using pandas (more powerful)\n", "import pandas as pd\n", "\n", "def read_csv_pandas(filename):\n", "    df = pd.read_csv(filename)\n", "    return df\n", "\n", "# Example usage\n", "# data = read_csv_pandas('data.csv')\n", "# print(data.head())'''\n", "    },\n", "    {\n", "        'instruction': 'Write a function to check if a string is a palindrome',\n", "        'response': '''def is_palindrome(s):\n", "    # Remove spaces and convert to lowercase\n", "    s = s.replace(\" \", \"\").lower()\n", "    return s == s[::-1]\n", "\n", "# Alternative method\n", "def is_palindrome_loop(s):\n", "    s = s.replace(\" \", \"\").lower()\n", "    left, right = 0, len(s) - 1\n", "    \n", "    while left < right:\n", "        if s[left] != s[right]:\n", "            return False\n", "        left += 1\n", "        right -= 1\n", "    return True\n", "\n", "# Example usage\n", "print(is_palindrome(\"racecar\"))  # Output: True\n", "print(is_palindrome(\"hello\"))    # Output: False'''\n", "    },\n", "    {\n", "        'instruction': 'Create a function to sort a list using bubble sort',\n", "        'response': '''def bubble_sort(arr):\n", "    n = len(arr)\n", "    \n", "    for i in range(n):\n", "        # Flag to optimize - if no swaps, list is sorted\n", "        swapped = False\n", "        \n", "        for j in range(0, n - i - 1):\n", "            if arr[j] > arr[j + 1]:\n", "                # Swap elements\n", "                arr[j], arr[j + 1] = arr[j + 1], arr[j]\n", "                swapped = True\n", "        \n", "        # If no swapping occurred, array is sorted\n", "        if not swapped:\n", "            break\n", "    \n", "    return arr\n", "\n", "# Example usage\n", "numbers = [64, 34, 25, 12, 22, 11, 90]\n", "sorted_numbers = bubble_sort(numbers.copy())\n", "print(sorted_numbers)  # Output: [11, 12, 22, 25, 34, 64, 90]'''\n", "    }\n", "]\n", "\n", "# Expand dataset by creating variations\n", "expanded_data = []\n", "for example in training_examples:\n", "    # Add original\n", "    expanded_data.append(example)\n", "    \n", "    # Add variations with different phrasings\n", "    variations = [\n", "        f\"How do I {example['instruction'].lower()}?\",\n", "        f\"Can you help me {example['instruction'].lower()}?\",\n", "        f\"Show me how to {example['instruction'].lower()}\",\n", "        f\"I need to {example['instruction'].lower()}\"\n", "    ]\n", "    \n", "    for variation in variations:\n", "        expanded_data.append({\n", "            'instruction': variation,\n", "            'response': example['response']\n", "        })\n", "\n", "# Create dataset\n", "dataset = Dataset.from_list(expanded_data)\n", "print(f'🎯 Created dataset with {len(dataset)} examples')\n", "print('✅ Dataset ready!')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load and setup Qwen3-8B model\n", "print('🧠 Loading Qwen3-8B model...')\n", "model_name = 'Qwen/Qwen3-8B'\n", "\n", "# Quantization for GPU efficiency\n", "if torch.cuda.is_available():\n", "    bnb_config = BitsAndBytesConfig(\n", "        load_in_4bit=True,\n", "        bnb_4bit_compute_dtype=torch.float16,\n", "        bnb_4bit_quant_type='nf4',\n", "        bnb_4bit_use_double_quant=True\n", "    )\n", "    print('✅ 4-bit quantization enabled')\n", "else:\n", "    bnb_config = None\n", "    print('⚠️ CPU mode - no quantization')\n", "\n", "# Load tokenizer\n", "print('📝 Loading tokenizer...')\n", "tokenizer = AutoTokenizer.from_pretrained(model_name, trust_remote_code=True)\n", "if tokenizer.pad_token is None:\n", "    tokenizer.pad_token = tokenizer.eos_token\n", "\n", "# Load model\n", "print('🚀 Loading model (this takes a few minutes)...')\n", "model_kwargs = {\n", "    'trust_remote_code': True,\n", "    'torch_dtype': torch.float16 if torch.cuda.is_available() else torch.float32\n", "}\n", "\n", "if bnb_config:\n", "    model_kwargs['quantization_config'] = bnb_config\n", "    model_kwargs['device_map'] = 'auto'\n", "\n", "model = AutoModelForCausalLM.from_pretrained(model_name, **model_kwargs)\n", "\n", "# Setup LoRA\n", "print('🔧 Setting up LoRA...')\n", "lora_config = LoraConfig(\n", "    task_type=TaskType.CAUSAL_LM,\n", "    r=16,\n", "    lora_alpha=32,\n", "    lora_dropout=0.1,\n", "    target_modules=['q_proj', 'k_proj', 'v_proj', 'o_proj', 'gate_proj', 'up_proj', 'down_proj']\n", ")\n", "\n", "model = get_peft_model(model, lora_config)\n", "model.print_trainable_parameters()\n", "print('✅ Model ready for training!')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Tokenize dataset\n", "print('🔤 Tokenizing dataset...')\n", "\n", "def tokenize_function(examples):\n", "    texts = []\n", "    for inst, resp in zip(examples['instruction'], examples['response']):\n", "        # Use Qwen3 chat format\n", "        text = f'<|im_start|>user\\n{inst}<|im_end|>\\n<|im_start|>assistant\\n{resp}<|im_end|>'\n", "        texts.append(text)\n", "    \n", "    tokenized = tokenizer(\n", "        texts,\n", "        truncation=True,\n", "        padding=False,\n", "        max_length=2048,\n", "        return_tensors=None\n", "    )\n", "    \n", "    tokenized['labels'] = tokenized['input_ids'].copy()\n", "    return tokenized\n", "\n", "# Tokenize\n", "tokenized_dataset = dataset.map(\n", "    tokenize_function,\n", "    batched=True,\n", "    remove_columns=dataset.column_names\n", ")\n", "\n", "# Split train/eval\n", "train_size = int(0.9 * len(tokenized_dataset))\n", "train_dataset = tokenized_dataset.select(range(train_size))\n", "eval_dataset = tokenized_dataset.select(range(train_size, len(tokenized_dataset)))\n", "\n", "print(f'📊 Train: {len(train_dataset)}, Eval: {len(eval_dataset)}')\n", "print('✅ Tokenization complete!')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Setup training\n", "print('🏋️ Setting up training...')\n", "\n", "output_dir = './qwen3-python-coder'\n", "os.makedirs(output_dir, exist_ok=True)\n", "\n", "# Training parameters\n", "training_args = TrainingArguments(\n", "    output_dir=output_dir,\n", "    num_train_epochs=3,\n", "    per_device_train_batch_size=4 if torch.cuda.is_available() else 1,\n", "    per_device_eval_batch_size=4 if torch.cuda.is_available() else 1,\n", "    gradient_accumulation_steps=4,\n", "    learning_rate=2e-4,\n", "    warmup_steps=10,\n", "    logging_steps=5,\n", "    save_steps=50,\n", "    eval_steps=50,\n", "    evaluation_strategy='steps',\n", "    save_strategy='steps',\n", "    load_best_model_at_end=True,\n", "    fp16=torch.cuda.is_available(),\n", "    dataloader_pin_memory=False,\n", "    remove_unused_columns=False,\n", "    report_to=None,\n", "    save_total_limit=2,\n", "    max_steps=100  # Quick training for demo\n", ")\n", "\n", "# Data collator\n", "data_collator = DataCollatorForLanguageModeling(\n", "    tokenizer=tokenizer,\n", "    mlm=False\n", ")\n", "\n", "# Create trainer\n", "trainer = Trainer(\n", "    model=model,\n", "    args=training_args,\n", "    train_dataset=train_dataset,\n", "    eval_dataset=eval_dataset,\n", "    data_collator=data_collator\n", ")\n", "\n", "print('✅ Training setup complete!')\n", "print(f'📊 Training samples: {len(train_dataset)}')\n", "print(f'⏱️ Estimated time: {\"15-30 min\" if torch.cuda.is_available() else \"2-3 hours\"}')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Start training\n", "print('🚀 Starting training...')\n", "print('📊 Progress will be shown below')\n", "print('='*50)\n", "\n", "start_time = datetime.now()\n", "print(f'🕐 Started: {start_time.strftime(\"%H:%M:%S\")}')\n", "\n", "# Train\n", "trainer.train()\n", "\n", "end_time = datetime.now()\n", "duration = end_time - start_time\n", "\n", "print('='*50)\n", "print(f'🎉 Training completed!')\n", "print(f'⏱️ Time: {str(duration).split(\".\")[0]}')\n", "print('✅ Success!')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Save model\n", "print('💾 Saving model...')\n", "\n", "trainer.save_model()\n", "tokenizer.save_pretrained(output_dir)\n", "\n", "# Save training info\n", "info = {\n", "    'model': 'Qwen/Qwen3-8B',\n", "    'method': 'LoRA fine-tuning',\n", "    'samples': len(train_dataset),\n", "    'time': str(duration).split('.')[0],\n", "    'completed': datetime.now().isoformat(),\n", "    'gpu_used': torch.cuda.is_available()\n", "}\n", "\n", "with open(f'{output_dir}/info.json', 'w') as f:\n", "    json.dump(info, f, indent=2)\n", "\n", "print(f'✅ Model saved to: {output_dir}')\n", "print('🎯 Training complete!')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test the trained model\n", "print('🧪 Testing your trained model...')\n", "\n", "def test_model(prompt):\n", "    text = f'<|im_start|>user\\n{prompt}<|im_end|>\\n<|im_start|>assistant\\n'\n", "    inputs = tokenizer(text, return_tensors='pt')\n", "    \n", "    if torch.cuda.is_available():\n", "        inputs = inputs.to(model.device)\n", "    \n", "    with torch.no_grad():\n", "        outputs = model.generate(\n", "            **inputs,\n", "            max_length=inputs.input_ids.shape[1] + 200,\n", "            temperature=0.7,\n", "            do_sample=True,\n", "            pad_token_id=tokenizer.eos_token_id\n", "        )\n", "    \n", "    response = tokenizer.decode(outputs[0], skip_special_tokens=True)\n", "    \n", "    if '<|im_start|>assistant\\n' in response:\n", "        response = response.split('<|im_start|>assistant\\n')[-1]\n", "    \n", "    return response.strip()\n", "\n", "# Test prompts\n", "test_prompts = [\n", "    'Write a function to calculate fi<PERSON><PERSON><PERSON> numbers',\n", "    'Create a class for a bank account',\n", "    'How do I sort a dictionary by values?'\n", "]\n", "\n", "for i, prompt in enumerate(test_prompts, 1):\n", "    print(f'\\n📝 Test {i}: {prompt}')\n", "    print('🤖 Response:')\n", "    try:\n", "        response = test_model(prompt)\n", "        # Show first 300 chars\n", "        print(response[:300] + ('...' if len(response) > 300 else ''))\n", "    except Exception as e:\n", "        print(f'Error: {e}')\n", "    print('-' * 50)\n", "\n", "print('\\n✅ Testing complete!')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Final summary\n", "print('\\n' + '='*60)\n", "print('🎉 TRAINING COMPLETE! 🎉')\n", "print('='*60)\n", "\n", "print(f'📁 Model saved: {output_dir}/')\n", "print(f'⏱️ Training time: {str(duration).split(\".\")[0]}')\n", "print(f'📊 Samples trained: {len(train_dataset)}')\n", "print(f'🎮 Device used: {\"GPU\" if torch.cuda.is_available() else \"CPU\"}')\n", "\n", "# Usage instructions\n", "usage = f'''\n", "## 🚀 How to Use Your Model:\n", "\n", "```python\n", "# Your model is ready to use right now!\n", "prompt = \"Write a Python function to reverse a list\"\n", "response = test_model(prompt)\n", "print(response)\n", "```\n", "\n", "## 📁 Files Created:\n", "- {output_dir}/ (your trained model)\n", "- info.json (training details)\n", "\n", "## 🎯 What Your Model Can Do:\n", "✅ Generate Python code from descriptions\n", "✅ Explain programming concepts\n", "✅ Debug and fix code issues\n", "✅ Provide coding best practices\n", "\n", "Training completed in {str(duration).split(\".\")[0]} using {\"GPU\" if torch.cuda.is_available() else \"CPU\"}!\n", "'''\n", "\n", "with open('USAGE.txt', 'w') as f:\n", "    f.write(usage)\n", "\n", "print('\\n🚀 Your Qwen3-8B Python Coder is ready!')\n", "print('📋 Usage instructions saved to USAGE.txt')\n", "print('\\n🎯 Happy coding! 🐍✨')\n", "\n", "if torch.cuda.is_available():\n", "    print(f'\\n💾 GPU Memory used: {torch.cuda.max_memory_allocated()/1e9:.1f}GB')"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}