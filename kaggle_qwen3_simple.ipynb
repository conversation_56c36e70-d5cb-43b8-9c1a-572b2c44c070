{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 🚀 Qwen3-8B Python Coder Training (Simple Version)\n", "\n", "**Simplified training pipeline - Compatible with all Jupyter environments**\n", "\n", "- 💰 Cost: **$0** (100% Free on Kaggle)\n", "- ⏱️ Time: ~2 hours\n", "- 🎮 GPU: P100/T4 (auto-assigned)\n", "- 📊 Datasets: 6,000 Python coding samples\n", "- 🧠 Model: Qwen3-8B with LoRA fine-tuning\n", "\n", "**Just run each cell one by one!**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Install packages\n", "import subprocess\n", "import sys\n", "\n", "packages = [\n", "    'transformers==4.45.0',\n", "    'datasets==2.14.0', \n", "    'peft==0.12.0',\n", "    'bitsandbytes==0.41.0',\n", "    'accelerate==0.25.0',\n", "    'scipy'\n", "]\n", "\n", "print('📦 Installing packages...')\n", "for package in packages:\n", "    try:\n", "        subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-q', package])\n", "        print(f'✅ {package}')\n", "    except Exception as e:\n", "        print(f'⚠️ Failed: {package} - {e}')\n", "\n", "print('✅ Installation complete!')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Setup and imports\n", "import os\n", "import torch\n", "import json\n", "from datetime import datetime\n", "\n", "# Import ML libraries\n", "from transformers import (\n", "    AutoTokenizer, AutoModelForCausalLM, BitsAndBytesConfig,\n", "    TrainingArguments, Trainer, DataCollatorForLanguageModeling\n", ")\n", "from datasets import Dataset, load_dataset\n", "from peft import LoraConfig, get_peft_model, TaskType\n", "\n", "# Check GPU\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "print(f'🎮 Device: {device}')\n", "if torch.cuda.is_available():\n", "    print(f'🚀 GPU: {torch.cuda.get_device_name(0)}')\n", "    print(f'💾 Memory: {torch.cuda.get_device_properties(0).total_memory/1e9:.1f}GB')\n", "else:\n", "    print('⚠️ No GPU - training will be slow!')\n", "\n", "print('✅ Setup complete!')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load datasets\n", "print('📥 Loading datasets...')\n", "\n", "# Simple dataset loading with error handling\n", "all_data = []\n", "\n", "# Dataset 1: Python instructions\n", "try:\n", "    print('📂 Loading Python instructions...')\n", "    ds1 = load_dataset('iamtarun/python_code_instructions_18k_alpaca', split='train')\n", "    ds1 = ds1.shuffle(seed=42).select(range(min(2000, len(ds1))))\n", "    \n", "    for item in ds1:\n", "        if 'instruction' in item and 'output' in item:\n", "            if 10 <= len(item['output']) <= 2000:\n", "                all_data.append({\n", "                    'instruction': item['instruction'],\n", "                    'response': item['output']\n", "                })\n", "    print(f'✅ Added {len([x for x in all_data])} samples')\n", "except Exception as e:\n", "    print(f'⚠️ Failed to load dataset 1: {e}')\n", "\n", "# Dataset 2: Code examples\n", "try:\n", "    print('📂 Loading code examples...')\n", "    ds2 = load_dataset('sahil2801/CodeAlpaca-20k', split='train')\n", "    ds2 = ds2.shuffle(seed=42).select(range(min(2000, len(ds2))))\n", "    \n", "    start_len = len(all_data)\n", "    for item in ds2:\n", "        if 'instruction' in item and 'output' in item:\n", "            if 10 <= len(item['output']) <= 2000:\n", "                all_data.append({\n", "                    'instruction': item['instruction'],\n", "                    'response': item['output']\n", "                })\n", "    print(f'✅ Added {len(all_data) - start_len} samples')\n", "except Exception as e:\n", "    print(f'⚠️ Failed to load dataset 2: {e}')\n", "\n", "# Create dataset\n", "if len(all_data) < 100:\n", "    print('❌ Not enough data loaded!')\n", "    raise ValueError('Insufficient training data')\n", "\n", "dataset = Dataset.from_list(all_data)\n", "print(f'🎯 Total samples: {len(dataset)}')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Setup model\n", "print('🧠 Loading Qwen3-8B...')\n", "model_name = 'Qwen/Qwen3-8B'\n", "\n", "# Quantization config\n", "bnb_config = BitsAndBytesConfig(\n", "    load_in_4bit=True,\n", "    bnb_4bit_compute_dtype=torch.float16,\n", "    bnb_4bit_quant_type='nf4',\n", "    bnb_4bit_use_double_quant=True\n", ")\n", "\n", "# Load tokenizer\n", "print('📝 Loading tokenizer...')\n", "tokenizer = AutoTokenizer.from_pretrained(model_name, trust_remote_code=True)\n", "if tokenizer.pad_token is None:\n", "    tokenizer.pad_token = tokenizer.eos_token\n", "\n", "# Load model\n", "print('🚀 Loading model...')\n", "model = AutoModelForCausalLM.from_pretrained(\n", "    model_name,\n", "    quantization_config=bnb_config,\n", "    device_map='auto',\n", "    trust_remote_code=True,\n", "    torch_dtype=torch.float16\n", ")\n", "\n", "# Setup LoRA\n", "print('🔧 Setting up LoRA...')\n", "lora_config = LoraConfig(\n", "    task_type=TaskType.CAUSAL_LM,\n", "    r=16,\n", "    lora_alpha=32,\n", "    lora_dropout=0.1,\n", "    target_modules=['q_proj', 'k_proj', 'v_proj', 'o_proj', 'gate_proj', 'up_proj', 'down_proj']\n", ")\n", "\n", "model = get_peft_model(model, lora_config)\n", "model.print_trainable_parameters()\n", "print('✅ Model ready!')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Tokenize data\n", "print('🔤 Tokenizing...')\n", "\n", "def tokenize_function(examples):\n", "    texts = []\n", "    for inst, resp in zip(examples['instruction'], examples['response']):\n", "        text = f'<|im_start|>user\\n{inst}<|im_end|>\\n<|im_start|>assistant\\n{resp}<|im_end|>'\n", "        texts.append(text)\n", "    \n", "    tokenized = tokenizer(\n", "        texts,\n", "        truncation=True,\n", "        padding=False,\n", "        max_length=2048,\n", "        return_tensors=None\n", "    )\n", "    \n", "    tokenized['labels'] = tokenized['input_ids'].copy()\n", "    return tokenized\n", "\n", "# Tokenize dataset\n", "tokenized_dataset = dataset.map(\n", "    tokenize_function,\n", "    batched=True,\n", "    remove_columns=dataset.column_names\n", ")\n", "\n", "# Split train/eval\n", "train_size = int(0.95 * len(tokenized_dataset))\n", "train_dataset = tokenized_dataset.select(range(train_size))\n", "eval_dataset = tokenized_dataset.select(range(train_size, len(tokenized_dataset)))\n", "\n", "print(f'📊 Train: {len(train_dataset)}, Eval: {len(eval_dataset)}')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Training setup\n", "print('🏋️ Setting up training...')\n", "\n", "# Output directory\n", "output_dir = './qwen3-python-coder'\n", "os.makedirs(output_dir, exist_ok=True)\n", "\n", "# Training arguments\n", "training_args = TrainingArguments(\n", "    output_dir=output_dir,\n", "    num_train_epochs=2,\n", "    per_device_train_batch_size=4,\n", "    per_device_eval_batch_size=4,\n", "    gradient_accumulation_steps=4,\n", "    learning_rate=2e-4,\n", "    warmup_steps=100,\n", "    logging_steps=10,\n", "    save_steps=500,\n", "    eval_steps=500,\n", "    evaluation_strategy='steps',\n", "    save_strategy='steps',\n", "    load_best_model_at_end=True,\n", "    fp16=True,\n", "    dataloader_pin_memory=False,\n", "    remove_unused_columns=False,\n", "    report_to=None,\n", "    save_total_limit=2\n", ")\n", "\n", "# Data collator\n", "data_collator = DataCollatorForLanguageModeling(\n", "    tokenizer=tokenizer,\n", "    mlm=False\n", ")\n", "\n", "# Create trainer\n", "trainer = Trainer(\n", "    model=model,\n", "    args=training_args,\n", "    train_dataset=train_dataset,\n", "    eval_dataset=eval_dataset,\n", "    data_collator=data_collator\n", ")\n", "\n", "print('✅ Training setup complete!')\n", "print(f'📊 Training samples: {len(train_dataset)}')\n", "print(f'📊 Evaluation samples: {len(eval_dataset)}')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Start training\n", "print('🚀 Starting training...')\n", "print('⏱️ This will take approximately 1-2 hours')\n", "print('📊 You will see progress updates below')\n", "\n", "start_time = datetime.now()\n", "print(f'🕐 Started at: {start_time.strftime(\"%H:%M:%S\")}')\n", "\n", "# Train the model\n", "trainer.train()\n", "\n", "end_time = datetime.now()\n", "duration = end_time - start_time\n", "print(f'\\n🎉 Training completed at: {end_time.strftime(\"%H:%M:%S\")}')\n", "print(f'⏱️ Total time: {str(duration).split(\".\")[0]}')\n", "print('✅ Training successful!')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Save model\n", "print('💾 Saving trained model...')\n", "\n", "# Save model and tokenizer\n", "trainer.save_model()\n", "tokenizer.save_pretrained(output_dir)\n", "\n", "# Save training info\n", "training_info = {\n", "    'model_name': 'Qwen/Qwen3-8B',\n", "    'training_method': 'LoRA',\n", "    'total_samples': len(train_dataset) + len(eval_dataset),\n", "    'train_samples': len(train_dataset),\n", "    'eval_samples': len(eval_dataset),\n", "    'training_time': str(duration).split('.')[0],\n", "    'completion_time': datetime.now().isoformat()\n", "}\n", "\n", "with open(f'{output_dir}/training_info.json', 'w') as f:\n", "    json.dump(training_info, f, indent=2)\n", "\n", "print(f'✅ Model saved to: {output_dir}')\n", "print('🎯 Your trained model is ready!')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test the model\n", "print('🧪 Testing your trained model...')\n", "\n", "def test_model(prompt, max_length=150):\n", "    \"\"\"Test the model with a prompt\"\"\"\n", "    formatted_prompt = f'<|im_start|>user\\n{prompt}<|im_end|>\\n<|im_start|>assistant\\n'\n", "    \n", "    inputs = tokenizer(formatted_prompt, return_tensors='pt').to(model.device)\n", "    \n", "    with torch.no_grad():\n", "        outputs = model.generate(\n", "            **inputs,\n", "            max_length=inputs.input_ids.shape[1] + max_length,\n", "            temperature=0.7,\n", "            do_sample=True,\n", "            pad_token_id=tokenizer.eos_token_id\n", "        )\n", "    \n", "    response = tokenizer.decode(outputs[0], skip_special_tokens=True)\n", "    \n", "    # Extract assistant response\n", "    if '<|im_start|>assistant\\n' in response:\n", "        response = response.split('<|im_start|>assistant\\n')[-1]\n", "    \n", "    return response.strip()\n", "\n", "# Test prompts\n", "test_prompts = [\n", "    'Write a Python function to calculate factorial',\n", "    'Create a simple calculator class',\n", "    'How to read a CSV file in Python?'\n", "]\n", "\n", "for i, prompt in enumerate(test_prompts, 1):\n", "    print(f'\\n📝 Test {i}: {prompt}')\n", "    try:\n", "        response = test_model(prompt)\n", "        print(f'🤖 Response: {response[:200]}...')\n", "    except Exception as e:\n", "        print(f'❌ Error: {e}')\n", "\n", "print('\\n✅ Model testing complete!')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create usage instructions\n", "print('📋 Creating usage instructions...')\n", "\n", "usage_instructions = f'''\n", "# 🎉 Your Qwen3-8B Python Coder is Ready!\n", "\n", "## 🚀 How to Use Your Trained Model:\n", "\n", "```python\n", "from transformers import AutoTokenizer, AutoModelForCausalLM\n", "from peft import PeftModel\n", "\n", "# Load base model\n", "base_model = AutoModelForCausalLM.from_pretrained(\"Qwen/Qwen3-8B\")\n", "tokenizer = AutoTokenizer.from_pretrained(\"Qwen/Qwen3-8B\")\n", "\n", "# Load your trained adapter\n", "model = PeftModel.from_pretrained(base_model, \"./qwen3-python-coder\")\n", "\n", "# Generate code\n", "prompt = \"Write a Python function to sort a list:\"\n", "formatted_prompt = f\"<|im_start|>user\\\\n{{prompt}}<|im_end|>\\\\n<|im_start|>assistant\\\\n\"\n", "inputs = tokenizer(formatted_prompt, return_tensors=\"pt\")\n", "outputs = model.generate(**inputs, max_length=200)\n", "response = tokenizer.decode(outputs[0], skip_special_tokens=True)\n", "print(response)\n", "```\n", "\n", "## 📊 Training Results:\n", "- Training samples: {len(train_dataset):,}\n", "- Training time: {str(duration).split(\".\")[0]}\n", "- Model: Qwen3-8B with LoRA fine-tuning\n", "\n", "## 🎯 What Your Model Can Do:\n", "✅ Generate Python code from natural language descriptions\n", "✅ Debug and fix existing code\n", "✅ Explain code functionality\n", "✅ Handle programming problems\n", "\n", "Enjoy your custom-trained Python coding assistant! 🚀\n", "'''\n", "\n", "with open('USAGE_INSTRUCTIONS.txt', 'w') as f:\n", "    f.write(usage_instructions)\n", "\n", "print('\\n' + '='*60)\n", "print('🎉 TRAINING COMPLETE! 🎉')\n", "print('='*60)\n", "print(f'📁 Model saved in: {output_dir}/')\n", "print(f'📋 Instructions: USAGE_INSTRUCTIONS.txt')\n", "print(f'⏱️ Total time: {str(duration).split(\".\")[0]}')\n", "print(f'📊 Training samples: {len(train_dataset):,}')\n", "print('\\n🚀 Your Qwen3-8B Python Coder is ready to use!')\n", "print('\\n📁 Files created:')\n", "print(f'  📄 {output_dir}/ (model files)')\n", "print('  📄 USAGE_INSTRUCTIONS.txt')\n", "print('\\n🎯 Happy coding! 🐍✨')"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}