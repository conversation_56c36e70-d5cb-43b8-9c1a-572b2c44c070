version: '3.8'

services:
  ai-trainer:
    build: .
    container_name: ai-trainer
    runtime: nvidia
    environment:
      - NVIDIA_VISIBLE_DEVICES=all
      - NVIDIA_DRIVER_CAPABILITIES=compute,utility
    volumes:
      - .:/workspace
      - ./models:/workspace/models
      - ./datasets:/workspace/datasets
      - ./outputs:/workspace/outputs
      - ~/.cache/huggingface:/root/.cache/huggingface
    working_dir: /workspace
    stdin_open: true
    tty: true
    shm_size: '16gb'
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]
