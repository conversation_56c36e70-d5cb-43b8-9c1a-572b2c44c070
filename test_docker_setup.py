#!/usr/bin/env python3
"""
Test Docker Setup - Verify all dependencies and GPU access
"""

import sys
import torch
import transformers
import datasets
import peft
import bitsandbytes

def test_gpu():
    """Test GPU availability"""
    print("🔧 Testing GPU Setup...")
    print(f"CUDA Available: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"CUDA Version: {torch.version.cuda}")
        print(f"GPU Count: {torch.cuda.device_count()}")
        for i in range(torch.cuda.device_count()):
            print(f"GPU {i}: {torch.cuda.get_device_name(i)}")
            print(f"  Memory: {torch.cuda.get_device_properties(i).total_memory / 1e9:.1f} GB")
    else:
        print("❌ No GPU detected - will use CPU (very slow)")

def test_libraries():
    """Test library versions"""
    print("\n📚 Testing Library Versions...")
    libraries = {
        "torch": torch.__version__,
        "transformers": transformers.__version__,
        "datasets": datasets.__version__,
        "peft": peft.__version__,
        "bitsandbytes": bitsandbytes.__version__,
    }
    
    for lib, version in libraries.items():
        print(f"✅ {lib}: {version}")

def test_model_loading():
    """Test loading a small model"""
    print("\n🤖 Testing Model Loading...")
    try:
        from transformers import AutoTokenizer, AutoModelForCausalLM
        
        # Use a tiny model for testing
        model_name = "microsoft/DialoGPT-small"
        print(f"Loading {model_name}...")
        
        tokenizer = AutoTokenizer.from_pretrained(model_name)
        model = AutoModelForCausalLM.from_pretrained(model_name)
        
        print("✅ Model loaded successfully!")
        print(f"Model parameters: {model.num_parameters():,}")
        
        # Test inference
        inputs = tokenizer("Hello, how are you?", return_tensors="pt")
        with torch.no_grad():
            outputs = model.generate(**inputs, max_length=20, do_sample=False)
        
        response = tokenizer.decode(outputs[0], skip_special_tokens=True)
        print(f"Test generation: {response}")
        
    except Exception as e:
        print(f"❌ Model loading failed: {e}")

def test_dataset_loading():
    """Test dataset loading"""
    print("\n📊 Testing Dataset Loading...")
    try:
        from datasets import load_dataset
        
        # Load a small dataset for testing
        print("Loading test dataset...")
        dataset = load_dataset("imdb", split="train[:100]")  # Just 100 samples
        print(f"✅ Dataset loaded: {len(dataset)} samples")
        print(f"Columns: {dataset.column_names}")
        
    except Exception as e:
        print(f"❌ Dataset loading failed: {e}")

def main():
    """Run all tests"""
    print("🐳 Docker AI Training Environment Test")
    print("=" * 50)
    
    test_gpu()
    test_libraries()
    test_model_loading()
    test_dataset_loading()
    
    print("\n" + "=" * 50)
    print("🎉 Setup test completed!")
    print("\nNext steps:")
    print("1. If GPU is detected, you're ready for training!")
    print("2. If no GPU, training will be very slow but still work")
    print("3. Run: python3 combined_dataset_training.py --help")

if __name__ == "__main__":
    main()
