#!/usr/bin/env python3
"""
AWS AI Trainer - Main Entry Point
Complete automated training pipeline for Qwen3-8B on AWS
"""

import os
import sys
import argparse
import logging
from datetime import datetime
from pathlib import Path

# Add aws directory to path
sys.path.append(str(Path(__file__).parent / "aws"))

from aws.aws_trainer import AWSTrainer
from aws.cost_optimizer import CostOptimizer
from aws.instance_manager import InstanceConfig
from aws.training_script_generator import TrainingConfig

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('aws_training.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def print_banner():
    """Print welcome banner"""
    banner = """
🚀 AWS AI Trainer - Qwen3-8B Python Coder
==========================================

Fully automated AWS training pipeline with:
✅ Spot instance optimization (~70% cost savings)
✅ Complete dataset combination and preprocessing  
✅ LoRA fine-tuning with 4-bit quantization
✅ Real-time progress monitoring
✅ Automatic model download
✅ Cost tracking and optimization

Estimated total cost: ~$0.75 for complete training
Expected training time: ~2 hours
"""
    print(banner)

def check_prerequisites():
    """Check if all prerequisites are met"""
    logger.info("Checking prerequisites...")
    
    # Check AWS credentials
    try:
        import boto3
        sts = boto3.client('sts')
        identity = sts.get_caller_identity()
        logger.info(f"✅ AWS credentials verified for account: {identity['Account']}")
    except Exception as e:
        logger.error("❌ AWS credentials not configured")
        logger.error("Please run: aws configure")
        return False
    
    # Check required packages
    required_packages = ['boto3', 'botocore']
    for package in required_packages:
        try:
            __import__(package)
            logger.info(f"✅ {package} available")
        except ImportError:
            logger.error(f"❌ {package} not installed")
            logger.error("Please run: pip install -r requirements.txt")
            return False
    
    logger.info("✅ All prerequisites met")
    return True

def get_cost_estimate(instance_type: str, use_spot: bool = True):
    """Get cost estimate for training"""
    optimizer = CostOptimizer()
    
    runtime_hours = 2.0  # Estimated training time
    on_demand_cost, spot_cost, savings_percentage = optimizer.calculate_spot_savings(
        instance_type, runtime_hours
    )
    
    estimated_cost = spot_cost if use_spot else on_demand_cost
    
    print(f"\n💰 Cost Estimate for {instance_type}:")
    print("=" * 40)
    print(f"Runtime: {runtime_hours} hours")
    print(f"On-demand cost: ${on_demand_cost:.2f}")
    if use_spot:
        print(f"Spot cost: ${spot_cost:.2f}")
        print(f"Savings: ${on_demand_cost - spot_cost:.2f} ({savings_percentage:.1f}%)")
        print(f"Total estimated: ${estimated_cost + 0.15:.2f} (including storage & transfer)")
    else:
        print(f"Total estimated: ${estimated_cost + 0.15:.2f} (including storage & transfer)")
    print("=" * 40)

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(
        description="AWS AI Trainer for Qwen3-8B",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Quick start with defaults (g5.xlarge spot instance)
  python train_aws.py
  
  # Use specific instance type
  python train_aws.py --instance-type g4dn.xlarge
  
  # Use on-demand instead of spot
  python train_aws.py --no-spot
  
  # Get cost estimates only
  python train_aws.py --estimate-only
  
  # Custom spot price
  python train_aws.py --spot-price 0.40
        """
    )
    
    parser.add_argument(
        "--instance-type", 
        default="g5.xlarge",
        choices=["g4dn.xlarge", "g5.xlarge", "g5.2xlarge", "p3.2xlarge"],
        help="EC2 instance type (default: g5.xlarge)"
    )
    
    parser.add_argument(
        "--no-spot", 
        action="store_true",
        help="Use on-demand instead of spot instances"
    )
    
    parser.add_argument(
        "--spot-price", 
        type=float,
        default=0.50,
        help="Maximum spot price (default: 0.50)"
    )
    
    parser.add_argument(
        "--estimate-only", 
        action="store_true",
        help="Show cost estimates only, don't start training"
    )
    
    parser.add_argument(
        "--region", 
        default="us-east-1",
        help="AWS region (default: us-east-1)"
    )
    
    parser.add_argument(
        "--output-dir", 
        default="./trained_model",
        help="Local directory for downloaded model (default: ./trained_model)"
    )
    
    args = parser.parse_args()
    
    # Print banner
    print_banner()
    
    # Check prerequisites
    if not check_prerequisites():
        sys.exit(1)
    
    # Show cost estimate
    get_cost_estimate(args.instance_type, not args.no_spot)
    
    if args.estimate_only:
        print("\n📊 Cost estimate complete. Use without --estimate-only to start training.")
        return
    
    # Confirm with user
    print(f"\n🎯 Ready to start training with:")
    print(f"   • Instance: {args.instance_type}")
    print(f"   • Type: {'Spot' if not args.no_spot else 'On-demand'}")
    print(f"   • Region: {args.region}")
    print(f"   • Output: {args.output_dir}")
    
    confirm = input("\n🚀 Start training? (y/N): ").strip().lower()
    if confirm not in ['y', 'yes']:
        print("Training cancelled.")
        return
    
    # Create trainer
    trainer = AWSTrainer(
        instance_type=args.instance_type,
        use_spot=not args.no_spot,
        spot_price=str(args.spot_price)
    )
    
    # Update region if specified
    trainer.instance_config.region = args.region
    
    start_time = datetime.now()
    
    try:
        # Run complete pipeline
        logger.info("🚀 Starting AWS training pipeline...")
        success = trainer.run_complete_pipeline()
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        if success:
            print(f"\n🎉 Training completed successfully!")
            print(f"⏱️  Total time: {str(duration).split('.')[0]}")
            print(f"📁 Model saved to: {args.output_dir}")
            
            # Generate cost report if instance was created
            if trainer.instance_id:
                from aws.cost_optimizer import CostOptimizer
                optimizer = CostOptimizer(args.region)
                cost_report = optimizer.generate_cost_report(trainer.instance_id)
                print(cost_report)
            
            print("\n🎯 Next steps:")
            print("1. Test your model with the inference scripts")
            print("2. Deploy to production or continue fine-tuning")
            print("3. Share your results with the community!")
            
        else:
            print(f"\n❌ Training failed after {str(duration).split('.')[0]}")
            print("Check aws_training.log for detailed error information")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⚠️  Training interrupted by user")
        logger.info("Cleaning up resources...")
        trainer.cleanup()
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        logger.error(f"Unexpected error: {e}")
        trainer.cleanup()
        sys.exit(1)

if __name__ == "__main__":
    main()
